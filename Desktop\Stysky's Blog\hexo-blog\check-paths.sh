#!/bin/bash

echo "=========================================="
echo "检查文件路径和注入配置..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 当前工作目录:"
pwd

echo "2. 检查目录结构..."
echo "Hexo 博客根目录:"
ls -la

echo "3. 检查 source 目录结构..."
echo "source 目录内容:"
ls -la source/

echo "4. 检查 _data 目录..."
if [ -d "source/_data" ]; then
    echo "✅ _data 目录存在"
    echo "_data 目录内容:"
    ls -la source/_data/
else
    echo "❌ _data 目录不存在"
fi

echo "5. 检查 css 目录..."
if [ -d "source/css" ]; then
    echo "✅ css 目录存在"
    echo "css 目录内容:"
    ls -la source/css/
else
    echo "❌ css 目录不存在"
fi

echo "6. 检查 js 目录..."
if [ -d "source/js" ]; then
    echo "✅ js 目录存在"
    echo "js 目录内容:"
    ls -la source/js/
else
    echo "❌ js 目录不存在"
fi

echo "7. 检查 static 目录..."
if [ -d "static" ]; then
    echo "✅ static 目录存在"
    echo "static 目录内容:"
    ls -la static/
else
    echo "❌ static 目录不存在"
fi

echo "8. 检查注入配置文件内容..."
if [ -f "source/_data/inject.yml" ]; then
    echo "✅ inject.yml 文件存在"
    echo "文件内容:"
    cat source/_data/inject.yml
else
    echo "❌ inject.yml 文件不存在"
fi

echo "9. 检查自定义 CSS 文件内容..."
if [ -f "source/css/custom.css" ]; then
    echo "✅ custom.css 文件存在"
    echo "文件大小:"
    ls -lh source/css/custom.css
    echo "文件前几行内容:"
    head -10 source/css/custom.css
else
    echo "❌ custom.css 文件不存在"
fi

echo "10. 检查自定义 JavaScript 文件内容..."
if [ -f "source/js/custom.js" ]; then
    echo "✅ custom.js 文件存在"
    echo "文件大小:"
    ls -lh source/js/custom.js
    echo "文件前几行内容:"
    head -10 source/js/custom.js
else
    echo "❌ custom.js 文件不存在"
fi

echo "11. 检查生成的 HTML 文件..."
if [ -f "public/index.html" ]; then
    echo "✅ index.html 文件存在"
    echo "检查是否包含自定义文件引用:"
    if grep -q "custom.css" public/index.html; then
        echo "✅ 找到 custom.css 引用"
        grep "custom.css" public/index.html
    else
        echo "❌ 未找到 custom.css 引用"
    fi
    
    if grep -q "custom.js" public/index.html; then
        echo "✅ 找到 custom.js 引用"
        grep "custom.js" public/index.html
    else
        echo "❌ 未找到 custom.js 引用"
    fi
else
    echo "❌ index.html 文件不存在"
fi

echo "12. 检查 Butterfly 主题是否支持 inject..."
if [ -d "themes/butterfly" ]; then
    echo "✅ Butterfly 主题存在"
    echo "检查主题是否支持 _data/inject.yml:"
    if [ -f "themes/butterfly/layout/includes/head.pug" ]; then
        echo "✅ 找到 head.pug 文件"
        if grep -q "inject" themes/butterfly/layout/includes/head.pug; then
            echo "✅ 主题支持 inject 功能"
        else
            echo "❌ 主题可能不支持 inject 功能"
        fi
    else
        echo "❌ 未找到 head.pug 文件"
    fi
else
    echo "❌ Butterfly 主题不存在"
fi

echo "=========================================="
echo "路径检查完成！"
echo "==========================================" 