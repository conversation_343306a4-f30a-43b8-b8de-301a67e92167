#!/bin/bash

echo "=========================================="
echo "验证修复结果..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查 YAML 语法..."
if hexo validate; then
    echo "✅ YAML 配置语法正确"
else
    echo "❌ YAML 配置语法错误"
    exit 1
fi

echo "2. 检查关键文件..."
echo "检查 _config.yml:"
if [ -f "_config.yml" ]; then
    echo "✅ 主配置文件存在"
    # 检查是否有语法错误
    if grep -q "hits_empty:" _config.yml; then
        echo "✅ 搜索配置正确"
    else
        echo "❌ 搜索配置缺失"
    fi
else
    echo "❌ 主配置文件不存在"
fi

echo "检查主题配置:"
if [ -f "themes/butterfly/_config.yml" ]; then
    echo "✅ 主题配置文件存在"
else
    echo "❌ 主题配置文件不存在"
fi

echo "3. 检查自定义文件..."
echo "检查注入配置:"
if [ -f "source/_data/inject.yml" ]; then
    echo "✅ 注入配置文件存在"
    cat source/_data/inject.yml
else
    echo "❌ 注入配置文件不存在"
fi

echo "检查自定义 CSS:"
if [ -f "source/css/custom.css" ]; then
    echo "✅ 自定义 CSS 存在"
    if grep -q "background-image" source/css/custom.css; then
        echo "✅ 背景图片配置正确"
    else
        echo "❌ 背景图片配置缺失"
    fi
    if grep -q "loading-bar" source/css/custom.css; then
        echo "✅ 加载进度条配置正确"
    else
        echo "❌ 加载进度条配置缺失"
    fi
else
    echo "❌ 自定义 CSS 不存在"
fi

echo "检查自定义 JavaScript:"
if [ -f "source/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 存在"
    if grep -q "showGreeting" source/js/custom.js; then
        echo "✅ 问候语句功能正确"
    else
        echo "❌ 问候语句功能缺失"
    fi
    if grep -q "PerformanceMonitor" source/js/custom.js; then
        echo "✅ 性能监控功能正确"
    else
        echo "❌ 性能监控功能缺失"
    fi
else
    echo "❌ 自定义 JavaScript 不存在"
fi

echo "4. 检查背景图片..."
if [ -f "static/background.jpg" ]; then
    echo "✅ 背景图片存在"
    ls -lh static/background.jpg
else
    echo "❌ 背景图片不存在"
    echo "请将背景图片命名为 background.jpg 并放在 static 文件夹中"
fi

echo "5. 检查生成的文件..."
echo "检查主页:"
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件已生成"
    if grep -q "custom.css" public/index.html; then
        echo "✅ 自定义 CSS 已注入"
    else
        echo "❌ 自定义 CSS 未注入"
    fi
    if grep -q "custom.js" public/index.html; then
        echo "✅ 自定义 JavaScript 已注入"
    else
        echo "❌ 自定义 JavaScript 未注入"
    fi
else
    echo "❌ 主页文件未生成"
fi

echo "检查自定义资源:"
if [ -f "public/css/custom.css" ]; then
    echo "✅ 自定义 CSS 已部署"
else
    echo "❌ 自定义 CSS 未部署"
fi

if [ -f "public/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 已部署"
else
    echo "❌ 自定义 JavaScript 未部署"
fi

echo "6. 检查页面..."
if [ -f "public/about/index.html" ]; then
    echo "✅ 关于页面已生成"
else
    echo "❌ 关于页面未生成"
fi

if [ -f "public/guestbook/index.html" ]; then
    echo "✅ 留言板页面已生成"
else
    echo "❌ 留言板页面未生成"
fi

if [ -f "public/shuoshuo/index.html" ]; then
    echo "✅ 吐槽页面已生成"
else
    echo "❌ 吐槽页面未生成"
fi

echo "7. 检查权限..."
if [ -r "$BLOG_DIR" ] && [ -w "$BLOG_DIR" ]; then
    echo "✅ 目录权限正常"
else
    echo "❌ 目录权限异常"
fi

echo "8. 测试 Hexo 命令..."
if hexo version > /dev/null 2>&1; then
    echo "✅ Hexo 命令正常"
else
    echo "❌ Hexo 命令异常"
fi

echo "=========================================="
echo "验证完成！"
echo "=========================================="
echo "如果所有项目都显示 ✅，说明修复成功"
echo "请访问: https://www.stysky.cloud"
echo "=========================================="
echo "如果仍有问题，请检查："
echo "1. 浏览器缓存（Ctrl+F5 强制刷新）"
echo "2. 背景图片是否正确放置"
echo "3. Nginx 配置是否正确"
echo "==========================================" 