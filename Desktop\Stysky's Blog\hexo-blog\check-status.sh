#!/bin/bash

echo "=========================================="
echo "检查博客状态..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查文件结构..."
echo "Hexo 博客目录:"
if [ -d "$BLOG_DIR" ]; then
    echo "✅ 博客目录存在"
else
    echo "❌ 博客目录不存在"
    exit 1
fi

echo "2. 检查主题..."
if [ -d "themes/butterfly" ]; then
    echo "✅ Butterfly 主题已安装"
else
    echo "❌ Butterfly 主题未安装"
fi

echo "3. 检查自定义文件..."
if [ -f "source/_data/inject.yml" ]; then
    echo "✅ 注入配置存在"
else
    echo "❌ 注入配置不存在"
fi

if [ -f "source/css/custom.css" ]; then
    echo "✅ 自定义 CSS 存在"
else
    echo "❌ 自定义 CSS 不存在"
fi

if [ -f "source/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 存在"
else
    echo "❌ 自定义 JavaScript 不存在"
fi

echo "4. 检查背景图片..."
if [ -f "static/background.jpg" ]; then
    echo "✅ 背景图片存在: static/background.jpg"
    ls -lh static/background.jpg
else
    echo "❌ 背景图片不存在"
    echo "请将背景图片命名为 background.jpg 并放在 static 文件夹中"
fi

echo "5. 检查生成的文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件已生成"
else
    echo "❌ 主页文件未生成"
fi

if [ -f "public/css/custom.css" ]; then
    echo "✅ 自定义 CSS 已部署"
else
    echo "❌ 自定义 CSS 未部署"
fi

if [ -f "public/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 已部署"
else
    echo "❌ 自定义 JavaScript 未部署"
fi

echo "6. 检查页面..."
if [ -f "public/about/index.html" ]; then
    echo "✅ 关于页面已生成"
else
    echo "❌ 关于页面未生成"
fi

if [ -f "public/guestbook/index.html" ]; then
    echo "✅ 留言板页面已生成"
else
    echo "❌ 留言板页面未生成"
fi

if [ -f "public/shuoshuo/index.html" ]; then
    echo "✅ 吐槽页面已生成"
else
    echo "❌ 吐槽页面未生成"
fi

echo "7. 检查权限..."
if [ -r "$BLOG_DIR" ] && [ -w "$BLOG_DIR" ]; then
    echo "✅ 目录权限正常"
else
    echo "❌ 目录权限异常"
fi

echo "=========================================="
echo "检查完成！"
echo "=========================================="
echo "如果所有项目都显示 ✅，说明配置正确"
echo "请访问: https://www.stysky.cloud"
echo "==========================================" 