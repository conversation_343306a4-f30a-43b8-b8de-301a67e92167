#!/bin/bash

echo "=========================================="
echo "测试博客功能..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查页面文件..."
echo "关于页面:"
if [ -f "source/about/index.md" ]; then
    echo "✅ 关于页面存在"
else
    echo "❌ 关于页面不存在"
fi

echo "留言板页面:"
if [ -f "source/guestbook/index.md" ]; then
    echo "✅ 留言板页面存在"
else
    echo "❌ 留言板页面不存在"
fi

echo "吐槽页面:"
if [ -f "source/shuoshuo/index.md" ]; then
    echo "✅ 吐槽页面存在"
else
    echo "❌ 吐槽页面不存在"
fi

echo "2. 检查自定义文件..."
echo "自定义 CSS:"
if [ -f "source/css/custom.css" ]; then
    echo "✅ 自定义 CSS 存在"
else
    echo "❌ 自定义 CSS 不存在"
fi

echo "自定义 JavaScript:"
if [ -f "source/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 存在"
else
    echo "❌ 自定义 JavaScript 不存在"
fi

echo "注入配置:"
if [ -f "source/_data/inject.yml" ]; then
    echo "✅ 注入配置存在"
else
    echo "❌ 注入配置不存在"
fi

echo "3. 检查主题配置..."
echo "Butterfly 主题:"
if [ -d "themes/butterfly" ]; then
    echo "✅ Butterfly 主题已安装"
else
    echo "❌ Butterfly 主题未安装"
fi

echo "主题配置:"
if [ -f "themes/butterfly/_config.yml" ]; then
    echo "✅ 主题配置文件存在"
else
    echo "❌ 主题配置文件不存在"
fi

echo "4. 检查菜单配置..."
if grep -q "关于" _config.yml; then
    echo "✅ 主配置包含关于菜单"
else
    echo "❌ 主配置缺少关于菜单"
fi

if grep -q "留言板" _config.yml; then
    echo "✅ 主配置包含留言板菜单"
else
    echo "❌ 主配置缺少留言板菜单"
fi

if grep -q "吐槽" _config.yml; then
    echo "✅ 主配置包含吐槽菜单"
else
    echo "❌ 主配置缺少吐槽菜单"
fi

echo "5. 检查生成的文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件已生成"
else
    echo "❌ 主页文件未生成"
fi

if [ -f "public/about/index.html" ]; then
    echo "✅ 关于页面已生成"
else
    echo "❌ 关于页面未生成"
fi

if [ -f "public/guestbook/index.html" ]; then
    echo "✅ 留言板页面已生成"
else
    echo "❌ 留言板页面未生成"
fi

if [ -f "public/shuoshuo/index.html" ]; then
    echo "✅ 吐槽页面已生成"
else
    echo "❌ 吐槽页面未生成"
fi

echo "6. 检查自定义资源..."
if [ -f "public/css/custom.css" ]; then
    echo "✅ 自定义 CSS 已部署"
else
    echo "❌ 自定义 CSS 未部署"
fi

if [ -f "public/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 已部署"
else
    echo "❌ 自定义 JavaScript 未部署"
fi

echo "=========================================="
echo "功能测试完成！"
echo "=========================================="
echo "如果所有项目都显示 ✅，说明功能配置正确"
echo "请访问以下页面测试功能："
echo "- 主页: https://www.stysky.cloud"
echo "- 关于: https://www.stysky.cloud/about/"
echo "- 留言板: https://www.stysky.cloud/guestbook/"
echo "- 吐槽: https://www.stysky.cloud/shuoshuo/"
echo "==========================================" 