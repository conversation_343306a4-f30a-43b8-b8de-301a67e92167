# Hexo 博客在宝塔面板上的操作指南

## 🚀 快速安装

### 1. 环境准备
- 在宝塔面板安装 Node.js 16.x
- 安装 Git

### 2. 一键安装
```bash
chmod +x install-hexo.sh
./install-hexo.sh
```

## ⚙️ 配置博客

### 1. 编辑主配置
编辑 `_config.yml`，设置站点信息

### 2. 安装插件
```bash
npm install hexo-generator-search hexo-wordcount hexo-generator-feed --save
```

### 3. 安装主题
```bash
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save
```

## 📄 创建页面

### 1. 关于页面
```bash
hexo new page about
```

### 2. 留言板
```bash
hexo new page guestbook
```

### 3. 吐槽页面
```bash
hexo new page shuoshuo
```

## 🌐 部署网站

### 1. 宝塔面板配置
- 创建网站，根目录设为 `/www/wwwroot/hexo-blog/public`
- 配置 SSL 证书
- 添加伪静态规则

### 2. 部署命令
```bash
./quick-deploy.sh
```

## 📝 日常使用

### 1. 新建文章
```bash
hexo new post "文章标题"
```

### 2. 本地预览
```bash
hexo server
```

### 3. 重新部署
```bash
hexo clean && hexo generate
```

## 🔧 故障排除

### 权限问题
```bash
chown -R www:www /www/wwwroot/hexo-blog
chmod -R 755 /www/wwwroot/hexo-blog
```

### 版本问题
```bash
node -v
npm -v
```

---

完成以上步骤后，您的 Hexo 博客就可以正常运行了！ 