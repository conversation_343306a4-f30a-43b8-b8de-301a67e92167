#!/bin/bash

# <PERSON><PERSON><PERSON>'s Blog 一键安装脚本

echo "=========================================="
echo "开始安装 <PERSON><PERSON><PERSON>'s Blog..."
echo "=========================================="

# 检查环境
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，请先在宝塔面板安装 Node.js 16.x"
    exit 1
fi

# 安装 Hexo
npm install -g hexo-cli

# 创建项目
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
mkdir -p "$BLOG_DIR"
cd "$BLOG_DIR"

# 初始化项目
hexo init . --yes
npm install

# 安装插件
npm install hexo-generator-search hexo-wordcount hexo-generator-feed --save
npm install hexo-generator-sitemap hexo-blog-encrypt hexo-related-posts --save

# 安装主题
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save

# 创建页面
hexo new page about
hexo new page guestbook
hexo new page shuoshuo

# 设置权限
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

# 创建部署脚本
cat > deploy.sh << 'EOF'
#!/bin/bash
cd /www/wwwroot/www.stysky.cloud/hexo-blog
hexo clean
hexo generate
chown -R www:www .
chmod -R 755 .
echo "部署完成！"
EOF

chmod +x deploy.sh

# 生成静态文件
hexo generate

echo "=========================================="
echo "安装完成！"
echo "=========================================="
echo "博客目录: $BLOG_DIR"
echo "部署命令: ./deploy.sh"
echo "本地预览: hexo server"
echo "==========================================" 