#!/bin/bash

echo "=========================================="
echo "修复注入问题..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查当前目录..."
pwd

echo "2. 确保在正确的目录中..."
if [ ! -f "_config.yml" ]; then
    echo "❌ 不在 Hexo 博客根目录中"
    echo "请确保在 /www/wwwroot/www.stysky.cloud/hexo-blog 目录中执行"
    exit 1
fi

echo "3. 创建正确的目录结构..."
mkdir -p source/_data
mkdir -p source/css
mkdir -p source/js

echo "4. 重新创建注入配置..."
cat > source/_data/inject.yml << 'EOF'
head:
  - <link rel="stylesheet" href="/css/custom.css">
body_end:
  - <script src="/js/custom.js"></script>
EOF

echo "5. 重新创建自定义 CSS..."
cat > source/css/custom.css << 'EOF'
/* 背景图片 */
body {
  background-image: url('/static/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  min-height: 100vh;
}

/* 加载进度条 */
#loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #49b1f5, #00c4b6);
  z-index: 9999;
  transition: width 0.3s ease;
}

/* 问候语句 */
#greeting-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(73, 177, 245, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 9998;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

#greeting-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* 性能监控 */
#performance-monitor {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9997;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 120px;
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

#performance-monitor .monitor-label {
  color: #ccc;
}

#performance-monitor .monitor-value {
  color: #49b1f5;
  font-weight: bold;
}
EOF

echo "6. 重新创建自定义 JavaScript..."
cat > source/js/custom.js << 'EOF'
// 加载进度条
function initLoadingBar() {
  const loadingBar = document.createElement('div');
  loadingBar.id = 'loading-bar';
  document.body.appendChild(loadingBar);
  
  let width = 0;
  const interval = setInterval(() => {
    width += Math.random() * 15;
    if (width >= 100) {
      width = 100;
      clearInterval(interval);
      setTimeout(() => {
        loadingBar.style.opacity = '0';
        setTimeout(() => loadingBar.remove(), 300);
      }, 200);
    }
    loadingBar.style.width = width + '%';
  }, 100);
}

// 问候语句
function showGreeting() {
  const hour = new Date().getHours();
  let greeting;
  
  if (hour < 6) greeting = '凌晨好！欢迎来到 Stysky 的博客！';
  else if (hour < 12) greeting = '早上好！欢迎来到 Stysky 的博客！';
  else if (hour < 18) greeting = '下午好！今天过得怎么样？';
  else greeting = '晚上好！夜深了，注意休息哦！';
  
  const greetingElement = document.createElement('div');
  greetingElement.id = 'greeting-message';
  greetingElement.textContent = greeting;
  document.body.appendChild(greetingElement);
  
  setTimeout(() => greetingElement.classList.add('show'), 500);
  setTimeout(() => {
    greetingElement.classList.remove('show');
    setTimeout(() => greetingElement.remove(), 500);
  }, 4000);
}

// 性能监控
class PerformanceMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frames = 0;
    this.init();
  }
  
  init() {
    const monitor = document.createElement('div');
    monitor.id = 'performance-monitor';
    monitor.innerHTML = `
      <div class="monitor-item">
        <span class="monitor-label">FPS:</span>
        <span class="monitor-value" id="fpsValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">延迟:</span>
        <span class="monitor-value" id="latencyValue">-- ms</span>
      </div>
    `;
    document.body.appendChild(monitor);
    
    this.updateFPS();
    this.updateLatency();
  }
  
  updateFPS() {
    const now = performance.now();
    this.frames++;
    
    if (now >= this.lastTime + 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      const fpsElement = document.getElementById('fpsValue');
      if (fpsElement) fpsElement.textContent = this.fps;
      this.frames = 0;
      this.lastTime = now;
    }
    
    requestAnimationFrame(() => this.updateFPS());
  }
  
  updateLatency() {
    const start = performance.now();
    fetch('/')
      .then(() => {
        const latency = Math.round(performance.now() - start);
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = latency + ' ms';
      })
      .catch(() => {
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = '-- ms';
      });
    
    setTimeout(() => this.updateLatency(), 5000);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');
  
  // 初始化加载进度条
  initLoadingBar();
  
  // 显示问候语句（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(showGreeting, 1000);
  }
  
  // 初始化性能监控
  new PerformanceMonitor();
});
EOF

echo "7. 检查文件是否创建成功..."
echo "检查注入配置:"
if [ -f "source/_data/inject.yml" ]; then
    echo "✅ inject.yml 创建成功"
    cat source/_data/inject.yml
else
    echo "❌ inject.yml 创建失败"
fi

echo "检查自定义 CSS:"
if [ -f "source/css/custom.css" ]; then
    echo "✅ custom.css 创建成功"
    echo "文件大小: $(ls -lh source/css/custom.css | awk '{print $5}')"
else
    echo "❌ custom.css 创建失败"
fi

echo "检查自定义 JavaScript:"
if [ -f "source/js/custom.js" ]; then
    echo "✅ custom.js 创建成功"
    echo "文件大小: $(ls -lh source/js/custom.js | awk '{print $5}')"
else
    echo "❌ custom.js 创建失败"
fi

echo "8. 清理并重新生成..."
hexo clean
hexo generate

echo "9. 检查生成的文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件生成成功"
    echo "检查注入结果:"
    if grep -q "custom.css" public/index.html; then
        echo "✅ 自定义 CSS 已注入"
        grep "custom.css" public/index.html
    else
        echo "❌ 自定义 CSS 未注入"
    fi
    
    if grep -q "custom.js" public/index.html; then
        echo "✅ 自定义 JavaScript 已注入"
        grep "custom.js" public/index.html
    else
        echo "❌ 自定义 JavaScript 未注入"
    fi
else
    echo "❌ 主页文件生成失败"
fi

echo "10. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "注入修复完成！"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "如果功能仍未显示，请："
echo "1. 强制刷新浏览器（Ctrl+F5）"
echo "2. 清除浏览器缓存"
echo "3. 检查浏览器控制台是否有错误"
echo "==========================================" 