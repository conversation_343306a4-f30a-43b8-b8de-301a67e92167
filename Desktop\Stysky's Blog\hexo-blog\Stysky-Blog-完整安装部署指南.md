# <PERSON><PERSON><PERSON>'s Blog 完整安装部署指南

## 📋 项目概述

本项目将您现有的 PHP 博客系统与现代化的 Hexo 框架相结合，创建一个具有独特设计风格的博客系统。参考 [blog.fzero.me](https://blog.fzero.me/) 的设计理念，但保持您自己的特色。

## 🏗️ 架构设计

### 混合架构方案
- **静态生成**: 使用 Hexo 生成静态文件，提升性能
- **动态功能**: 保留部分 PHP 功能（留言板、吐槽等）
- **现代化设计**: 采用 Butterfly 主题，支持响应式设计
- **SEO 优化**: 自动生成站点地图、RSS 订阅等

## 🚀 安装步骤

### 第一步：环境准备

#### 1.1 在宝塔面板安装 Node.js
```bash
# 在宝塔面板中
软件商店 → 搜索"Node.js版本管理器" → 安装 → 选择 16.x 版本
```

#### 1.2 安装 Git
```bash
# CentOS
yum install git -y

# Ubuntu
apt-get install git -y
```

#### 1.3 检查环境
```bash
node -v  # 应该显示 v16.x.x
npm -v   # 应该显示 8.x.x 或更高
git --version
```

### 第二步：安装 Hexo

#### 2.1 全局安装 Hexo CLI
```bash
npm install -g hexo-cli
```

#### 2.2 验证安装
```bash
hexo version
```

### 第三步：创建博客项目

#### 3.1 创建项目目录
```bash
# 进入您的网站目录
cd /www/wwwroot/www.stysky.cloud

# 创建 Hexo 项目目录
mkdir hexo-blog
cd hexo-blog
```

#### 3.2 初始化 Hexo 项目
```bash
hexo init .
npm install
```

### 第四步：安装插件和主题

#### 4.1 安装推荐插件
```bash
# 搜索功能
npm install hexo-generator-search --save

# 字数统计
npm install hexo-wordcount --save

# RSS 订阅
npm install hexo-generator-feed --save

# 站点地图
npm install hexo-generator-sitemap --save

# 百度站点地图
npm install hexo-generator-baidu-sitemap --save

# 文章加密
npm install hexo-blog-encrypt --save

# 相关文章推荐
npm install hexo-related-posts --save

# 图片懒加载
npm install hexo-lazyload-image --save

# 代码高亮优化
npm install hexo-prism-plugin --save
```

#### 4.2 安装 Butterfly 主题
```bash
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save
```

### 第五步：配置博客

#### 5.1 配置主配置文件 `_config.yml`
```yaml
# Hexo Configuration
title: Stysky's Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
keywords: [Hexo, 博客, 技术, 生活, 运维, 前端, 后端, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 分类与标签
category_dir: categories
tag_dir: tags
archive_dir: archives

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
```

#### 5.2 配置主题文件 `themes/butterfly/_config.yml`
```yaml
# Butterfly 主题配置 - Stysky 定制版

# 主题颜色配置（Stysky 风格）
theme_color:
  enable: true
  main: "#2d8cf0"  # 主色调：深蓝色
  paginator: "#19be6b"  # 分页：绿色
  button_hover: "#ff9900"  # 按钮悬停：橙色
  text_selection: "#19be6b"  # 文本选择：绿色
  link_color: "#515a6e"  # 链接：深灰色
  meta_color: "#808695"  # 元信息：中灰色
  hr_color: "#e8eaec"  # 分割线：浅灰色
  code_color: "#f47466"  # 代码：红色
  code_bg_color: "#f8f8f8"  # 代码背景：浅灰
  toc_color: "#19be6b"  # 目录：绿色
  blockquote_padding_color: "#2d8cf0"  # 引用：蓝色
  blockquote_background_color: "#f8f9fa"  # 引用背景：浅灰

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/stysky || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope
  twitter: https://twitter.com/stysky || fab fa-twitter
  facebook: https://facebook.com/stysky || fab fa-facebook
  youtube: https://youtube.com/stysky || fab fa-youtube
  instagram: https://instagram.com/stysky || fab fa-instagram
  linkedin: https://linkedin.com/in/stysky || fab fa-linkedin

# 侧边栏配置
aside:
  enable: true
  hide: false
  button: true
  mobile: true
  position: right
  display:
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: 允许一切自然发生
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me
      link: https://github.com/stysky
  card_announcement:
    enable: true
    content: 欢迎来到 Stysky's Blog！
  card_recent_post:
    enable: true
    limit: 5
    sort: date
    sort_order: -1
  card_categories:
    enable: true
    limit: 8
    sort: name
    sort_order: 1
  card_tags:
    enable: true
    limit: 40
    sort: name
    sort_order: 1
    color: false
    initialize: false
  card_archives:
    enable: true
    type: monthly
    format: MMMM YYYY
    order: -1
    limit: 8
    sort: date
    sort_order: -1
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: true

# 文章目录
toc:
  enable: true
  number: true
  expand: false
  style_simple: false

# 代码高亮
highlight_theme: mac
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 文章配置
post:
  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

  # 文章推荐
  recommend:
    enable: true
    limit: 6
    date: 90

  # 文章目录
  toc:
    enable: true
    number: true
    expand: false
    style_simple: false

# 搜索配置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 页面配置
page:
  # 关于页面
  about:
    enable: true
    avatar: /images/avatar.jpg
    name: Stysky
    subtitle: 允许一切自然发生
    type: self
    comment: true
    social:
      github: https://github.com/stysky
      email: mailto:<EMAIL>
      twitter: https://twitter.com/stysky

  # 留言板页面
  guestbook:
    enable: true
    comment: true

  # 吐槽页面
  shuoshuo:
    enable: true
    comment: true

# 动画效果
animate:
  enable: true
  title: true
  sidebar: true
  post_list: true
  post_item: true
  post_header: true
  post_body: true
  post_footer: true
  comment: true

# 特效配置
pjax:
  enable: true
  exclude:
    - /music/
    - /js/
    - /css/
    - /img/

# 统计配置
statistics:
  enable: true
  busuanzi:
    enable: true
    total_visitors: true
    total_views: true
    post_views: true

# 其他配置
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png
```

### 第六步：创建页面

#### 6.1 创建关于页面
```bash
hexo new page about
```

编辑 `source/about/index.md`：
```markdown
---
title: 关于
date: 2024-01-01 00:00:00
type: about
---

# 关于 Stysky

## 👨‍💻 个人简介

你好，我是 Stysky，一个热爱技术和生活的程序员。

## 🎯 博客定位

这个博客主要分享：
- 技术文章和教程
- 生活感悟和思考
- 项目经验和总结
- 学习笔记和心得

## 🛠️ 技术栈

- **前端**: HTML, CSS, JavaScript, Vue.js, React
- **后端**: PHP, Node.js, Python
- **数据库**: MySQL, MongoDB, Redis
- **运维**: Linux, Docker, Kubernetes
- **其他**: Git, Docker, CI/CD

## 📫 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [@stysky](https://github.com/stysky)
- **Twitter**: [@stysky](https://twitter.com/stysky)

## 🎨 博客特色

- 简洁优雅的设计风格
- 响应式布局，支持多设备
- 丰富的交互功能
- 良好的阅读体验

---

感谢您的访问！如果觉得文章有帮助，请点个赞支持一下。
```

#### 6.2 创建留言板页面
```bash
hexo new page guestbook
```

编辑 `source/guestbook/index.md`：
```markdown
---
title: 留言板
date: 2024-01-01 00:00:00
type: guestbook
---

# 留言板

欢迎在这里留言交流！

## 💬 留言规则

1. 文明发言，互相尊重
2. 可以讨论技术问题
3. 可以分享生活感悟
4. 禁止恶意攻击和广告

## 🎯 留言主题

- 技术交流
- 生活分享
- 建议反馈
- 合作洽谈

---

期待您的留言！
```

#### 6.3 创建吐槽页面
```bash
hexo new page shuoshuo
```

编辑 `source/shuoshuo/index.md`：
```markdown
---
title: 日常吐槽
date: 2024-01-01 00:00:00
type: shuoshuo
---

# 日常吐槽

这里记录一些日常的碎碎念...

## 📝 吐槽分类

- 技术相关
- 生活感悟
- 工作心得
- 学习笔记

---

生活不易，吐槽有益！
```

### 第七步：创建文章模板

#### 7.1 创建文章模板 `scaffolds/post.md`
```markdown
---
title: {{ title }}
date: {{ date }}
updated: {{ date }}
categories: 
  - 技术
tags: 
  - 标签1
  - 标签2
description: 文章摘要，请在此处填写文章的主要内容和亮点
keywords: 
  - 关键词1
  - 关键词2
  - 关键词3
toc: true
toc_number: true
toc_style_simple: false
copyright: true
mathjax: false
comments: true
top_img: /images/post-bg.jpg
cover: /images/post-cover.jpg
abbrlink: 
reprint_policy: cc_by
headimg: /images/post-head.jpg
---

<!-- 文章头部信息 -->
<div class="note info">
  <p>📝 本文发布于 {{ date }}，最后更新于 {{ date }}</p>
</div>

<!-- 文章目录 -->
<div class="note warning">
  <p>📋 文章目录</p>
</div>

<!-- 文章内容 -->
## 前言

在这里写文章的开头部分，简要介绍文章的主题和内容。

## 正文内容

### 1. 第一个章节

在这里写第一个章节的内容。

```bash
# 示例代码
echo "Hello World"
```

### 2. 第二个章节

在这里写第二个章节的内容。

> 💡 **提示**：这是一个引用块，可以用来突出重要信息。

### 3. 总结

在这里写文章的总结部分。

## 参考资料

- [参考链接1](https://example.com)
- [参考链接2](https://example.com)

## 相关文章

<!-- 这里会自动显示相关文章 -->

---

<div class="note success">
  <p>🎉 感谢您的阅读！如果觉得文章有帮助，请点个赞支持一下。</p>
</div>
```

### 第八步：宝塔面板配置

#### 8.1 创建网站
1. 登录宝塔面板
2. 进入【网站】→【添加站点】
3. 域名：www.stysky.cloud
4. 根目录：`/www/wwwroot/www.stysky.cloud/hexo-blog/public`
5. 启用 SSL 证书

#### 8.2 配置伪静态
在网站设置中添加：
```nginx
location / {
    try_files $uri $uri/ /index.html;
}

# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 8.3 配置反向代理（可选）
如果需要保留部分 PHP 功能，可以配置反向代理：
```nginx
# 留言板和吐槽页面使用 PHP
location ~ ^/(guestbook|shuoshuo) {
    proxy_pass http://127.0.0.1:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
```

### 第九步：创建部署脚本

#### 9.1 创建部署脚本 `deploy.sh`
```bash
#!/bin/bash

# Stysky's Blog 部署脚本

echo "=========================================="
echo "开始部署 Stysky's Blog..."
echo "=========================================="

# 设置博客目录
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"

# 检查博客目录是否存在
if [ ! -d "$BLOG_DIR" ]; then
    echo "错误：博客目录不存在"
    exit 1
fi

# 进入博客目录
cd "$BLOG_DIR"

# 清理缓存
echo "正在清理缓存..."
hexo clean

# 安装依赖（如果有更新）
echo "正在检查依赖..."
npm install

# 生成静态文件
echo "正在生成静态文件..."
hexo generate

# 检查生成是否成功
if [ ! -d "public" ]; then
    echo "错误：静态文件生成失败"
    exit 1
fi

# 设置权限
echo "正在设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

# 备份旧文件（可选）
echo "正在备份旧文件..."
if [ -d "/www/wwwroot/www.stysky.cloud/backup" ]; then
    rm -rf /www/wwwroot/www.stysky.cloud/backup/*
else
    mkdir -p /www/wwwroot/www.stysky.cloud/backup
fi

# 复制 PHP 文件到备份目录
cp -r /www/wwwroot/www.stysky.cloud/*.php /www/wwwroot/www.stysky.cloud/backup/ 2>/dev/null || true

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "博客目录: $BLOG_DIR"
echo "网站根目录: $BLOG_DIR/public"
echo "访问地址: https://www.stysky.cloud"
echo "=========================================="
echo "常用命令："
echo "新建文章: hexo new post '文章标题'"
echo "本地预览: hexo server"
echo "重新部署: ./deploy.sh"
echo "=========================================="
```

#### 9.2 设置脚本权限
```bash
chmod +x deploy.sh
```

### 第十步：测试部署

#### 10.1 运行部署脚本
```bash
./deploy.sh
```

#### 10.2 本地预览
```bash
hexo server
# 访问 http://localhost:4000
```

#### 10.3 检查网站
访问 https://www.stysky.cloud 检查部署效果

## 📝 日常使用

### 新建文章
```bash
cd /www/wwwroot/www.stysky.cloud/hexo-blog
hexo new post "文章标题"
```

### 编辑文章
```bash
vim source/_posts/文章标题.md
```

### 本地预览
```bash
hexo server
```

### 重新部署
```bash
./deploy.sh
```

## 🔧 故障排除

### 权限问题
```bash
chown -R www:www /www/wwwroot/www.stysky.cloud/hexo-blog
chmod -R 755 /www/wwwroot/www.stysky.cloud/hexo-blog
```

### Node.js 版本问题
```bash
node -v
npm -v
```

### 主题问题
```bash
# 重新安装主题
rm -rf themes/butterfly
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
```

### 插件问题
```bash
# 清理并重新安装
rm -rf node_modules
npm install
```

## 🎨 自定义样式

### 创建自定义 CSS
```bash
mkdir -p source/css
touch source/css/custom.css
```

编辑 `source/css/custom.css`：
```css
/* Stysky's Blog 自定义样式 */

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 文章标题样式 */
.post-title {
    font-weight: 600;
    color: #2d8cf0;
}

/* 代码块样式 */
.highlight {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 引用块样式 */
blockquote {
    border-left: 4px solid #2d8cf0;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(45,140,240,0.3);
}

/* 卡片样式 */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

/* 导航栏样式 */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.9);
}

/* 侧边栏样式 */
.aside {
    border-radius: 12px;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card {
        margin: 0.5rem;
    }
    
    .navbar {
        padding: 0.5rem 1rem;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .card {
        background: #2a2a2a;
        color: #e0e0e0;
    }
}
```

## 📊 性能优化

### 启用 Gzip 压缩
在宝塔面板网站设置中启用 Gzip 压缩

### 配置缓存
在 Nginx 配置中添加：
```nginx
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### CDN 加速
配置 CDN 加速静态资源

## 🔒 安全建议

1. 定期更新 Node.js 和 npm 包
2. 配置防火墙规则
3. 启用 WAF 防护
4. 定期备份数据
5. 监控异常访问

## 📈 监控和维护

### 日志监控
```bash
# 查看 Nginx 访问日志
tail -f /www/wwwlogs/www.stysky.cloud.log

# 查看错误日志
tail -f /www/wwwlogs/www.stysky.cloud.error.log
```

### 性能监控
在宝塔面板中启用网站监控功能

## 🎯 特色功能

### 1. 混合架构
- 静态页面使用 Hexo 生成，提升性能
- 动态功能保留 PHP 实现，保持灵活性

### 2. 独特设计风格
- 深蓝色主色调，体现专业性
- 绿色辅助色，增加活力
- 橙色强调色，突出重点

### 3. 丰富功能
- 文章目录自动生成
- 相关文章推荐
- 搜索功能
- RSS 订阅
- 站点地图

### 4. 良好体验
- 响应式设计
- 平滑动画效果
- 暗色模式支持
- 移动端优化

---

完成以上步骤后，您将拥有一个功能完整、设计独特的博客系统！

## 📞 技术支持

如果遇到问题，请：
1. 检查错误日志
2. 查看控制台输出
3. 参考官方文档
4. 联系技术支持

---

**Stysky's Blog** - 允许一切自然发生 🚀 