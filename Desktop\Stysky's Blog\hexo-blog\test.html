<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>'s Blog - 功能测试</title>
    <link rel="stylesheet" href="/css/custom.css">
</head>
<body>
    <nav id="nav">
        <span id="blog-info">
            <a class="nav-site-title" href="/">St<PERSON><PERSON>'s Blog</a>
        </span>
        <div id="menus">
            <div class="menus_items">
                <div class="menus_item">
                    <a href="/"><i class="fas fa-home"></i> 首页</a>
                </div>
                <div class="menus_item">
                    <a href="/categories/"><i class="fas fa-folder-open"></i> 分类</a>
                </div>
                <div class="menus_item">
                    <a href="/tags/"><i class="fas fa-tags"></i> 标签</a>
                </div>
                <div class="menus_item">
                    <a href="/archives/"><i class="fas fa-archive"></i> 归档</a>
                </div>
                <div class="menus_item">
                    <a href="/about/"><i class="fas fa-heart"></i> 关于</a>
                </div>
            </div>
            <div id="search-button">
                <span class="site-page social-icon search">
                    <i class="fas fa-search fa-fw"></i>
                    <span> 搜索</span>
                </span>
            </div>
        </div>
    </nav>

    <main style="margin-top: 80px; padding: 2rem;">
        <h1>Stysky's Blog 功能测试页面</h1>
        <p>这是一个测试页面，用于验证我们实现的功能：</p>
        <ul>
            <li>✅ 现代化导航栏设计</li>
            <li>✅ 加载进度条</li>
            <li>✅ 问候语聊天弹窗</li>
            <li>✅ IP地址信息显示</li>
            <li>✅ 性能监控显示</li>
            <li>✅ 1.jpg背景图片</li>
        </ul>
        
        <div style="margin-top: 2rem;">
            <button onclick="showGreeting()" style="margin-right: 1rem;">测试问候语</button>
            <button onclick="showIPLocation()" style="margin-right: 1rem;">测试IP信息</button>
            <button onclick="initLoadingBar()">测试加载条</button>
        </div>
    </main>

    <script src="/js/custom.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
