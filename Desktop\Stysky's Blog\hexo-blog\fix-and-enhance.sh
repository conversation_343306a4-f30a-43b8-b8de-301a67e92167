#!/bin/bash

echo "=========================================="
echo "修复和增强 <PERSON>ys<PERSON>'s Blog..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查当前状态..."
if [ ! -d "themes/butterfly" ]; then
    echo "安装 Butterfly 主题..."
    git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
    npm install hexo-renderer-pug hexo-renderer-stylus --save
fi

echo "2. 创建页面..."
hexo new page about
hexo new page guestbook
hexo new page shuoshuo

echo "3. 配置页面内容..."
cat > source/about/index.md << 'EOF'
---
title: 关于
date: 2024-01-01 00:00:00
type: about
---

# 关于 Stysky

## 👨‍💻 个人简介

你好，我是 Stysky，一个热爱技术和生活的程序员。

## 🎯 博客定位

这个博客主要分享：
- 技术文章和教程
- 生活感悟和思考
- 项目经验和总结
- 学习笔记和心得

## 🛠️ 技术栈

- **前端**: HTML, CSS, JavaScript, Vue.js, React
- **后端**: PHP, Node.js, Python
- **数据库**: MySQL, MongoDB, Redis
- **运维**: Linux, Docker, Kubernetes
- **其他**: Git, Docker, CI/CD

## 📫 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [@stysky](https://github.com/stysky)
- **Twitter**: [@stysky](https://twitter.com/stysky)

## 🎨 博客特色

- 简洁优雅的设计风格
- 响应式布局，支持多设备
- 丰富的交互功能
- 良好的阅读体验

---

感谢您的访问！如果觉得文章有帮助，请点个赞支持一下。
EOF

cat > source/guestbook/index.md << 'EOF'
---
title: 留言板
date: 2024-01-01 00:00:00
type: guestbook
---

# 留言板

欢迎在这里留言交流！

## 💬 留言规则

1. 文明发言，互相尊重
2. 可以讨论技术问题
3. 可以分享生活感悟
4. 禁止恶意攻击和广告

## 🎯 留言主题

- 技术交流
- 生活分享
- 建议反馈
- 合作洽谈

---

期待您的留言！
EOF

cat > source/shuoshuo/index.md << 'EOF'
---
title: 日常吐槽
date: 2024-01-01 00:00:00
type: shuoshuo
---

# 日常吐槽

这里记录一些日常的碎碎念...

## 📝 吐槽分类

- 技术相关
- 生活感悟
- 工作心得
- 学习笔记

---

生活不易，吐槽有益！
EOF

echo "4. 配置主配置文件..."
cat > _config.yml << 'EOF'
# Hexo Configuration
title: Stysky's Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
keywords: [Hexo, 博客, 技术, 生活, 运维, 前端, 后端, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航 - 参考 blog.fzero.me 风格
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
EOF

echo "5. 配置 Butterfly 主题（包含背景图片）..."
cat > themes/butterfly/_config.yml << 'EOF'
# Butterfly 主题配置 - Stysky 定制版

# 主题颜色配置（参考 blog.fzero.me）
theme_color:
  enable: true
  main: "#49b1f5"  # 主色调：蓝色
  paginator: "#00c4b6"  # 分页：青色
  button_hover: "#FF7242"  # 按钮悬停：橙色
  text_selection: "#00c4b6"  # 文本选择：青色
  link_color: "#99a9bf"  # 链接：灰色
  meta_color: "#858585"  # 元信息：中灰色
  hr_color: "#A4D8FA"  # 分割线：浅蓝色
  code_color: "#F47466"  # 代码：红色
  code_bg_color: "#F8F8F8"  # 代码背景：浅灰
  toc_color: "#00c4b6"  # 目录：青色
  blockquote_padding_color: "#49b1f5"  # 引用：蓝色
  blockquote_background_color: "#49b1f5"  # 引用背景：蓝色

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/stysky || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope
  twitter: https://twitter.com/stysky || fab fa-twitter

# 侧边栏配置
aside:
  enable: true
  hide: false
  button: true
  mobile: true
  position: right
  display:
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: 允许一切自然发生
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me
      link: https://github.com/stysky
  card_announcement:
    enable: true
    content: 欢迎来到 Stysky's Blog！
  card_recent_post:
    enable: true
    limit: 5
    sort: date
    sort_order: -1
  card_categories:
    enable: true
    limit: 8
    sort: name
    sort_order: 1
  card_tags:
    enable: true
    limit: 40
    sort: name
    sort_order: 1
    color: false
    initialize: false
  card_archives:
    enable: true
    type: monthly
    format: MMMM YYYY
    order: -1
    limit: 8
    sort: date
    sort_order: -1
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: true

# 文章目录
toc:
  enable: true
  number: true
  expand: false
  style_simple: false

# 代码高亮
highlight_theme: mac
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 文章配置
post:
  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

  # 文章推荐
  recommend:
    enable: true
    limit: 6
    date: 90

  # 文章目录
  toc:
    enable: true
    number: true
    expand: false
    style_simple: false

# 搜索配置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 页面配置
page:
  # 关于页面
  about:
    enable: true
    avatar: /images/avatar.jpg
    name: Stysky
    subtitle: 允许一切自然发生
    type: self
    comment: true
    social:
      github: https://github.com/stysky
      email: mailto:<EMAIL>
      twitter: https://twitter.com/stysky

  # 留言板页面
  guestbook:
    enable: true
    comment: true

  # 吐槽页面
  shuoshuo:
    enable: true
    comment: true

# 动画效果
animate:
  enable: true
  title: true
  sidebar: true
  post_list: true
  post_item: true
  post_header: true
  post_body: true
  post_footer: true
  comment: true

# 特效配置
pjax:
  enable: true
  exclude:
    - /music/
    - /js/
    - /css/
    - /img/

# 统计配置
statistics:
  enable: true
  busuanzi:
    enable: true
    total_visitors: true
    total_views: true
    post_views: true

# 其他配置
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png
EOF

echo "6. 创建自定义样式和脚本..."
mkdir -p source/_data
mkdir -p source/css
mkdir -p source/js

echo "7. 创建注入配置..."
cat > source/_data/inject.yml << 'EOF'
head:
  - <link rel="stylesheet" href="/css/custom.css">

body_end:
  - <script src="/js/custom.js"></script>
EOF

echo "8. 创建自定义 CSS（包含背景图片）..."
cat > source/css/custom.css << 'EOF'
/* 自定义样式 - 参考 blog.fzero.me */

/* 背景图片设置 */
body {
  background-image: url('/static/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  min-height: 100vh;
}

/* 页面内容背景 */
#container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  margin: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 加载进度条 */
#loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #49b1f5, #00c4b6);
  z-index: 9999;
  transition: width 0.3s ease;
}

/* 问候语句 */
#greeting-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(73, 177, 245, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 9998;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

#greeting-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* 性能监控弹窗 */
#performance-monitor {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9997;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 120px;
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

#performance-monitor .monitor-item:last-child {
  margin-bottom: 0;
}

#performance-monitor .monitor-label {
  color: #ccc;
}

#performance-monitor .monitor-value {
  color: #49b1f5;
  font-weight: bold;
}

/* 导航菜单样式优化 */
.navbar {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(73, 177, 245, 0.1);
}

.nav-links a {
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 8px 12px;
}

.nav-links a:hover {
  background: rgba(73, 177, 245, 0.1);
  color: #49b1f5;
  transform: translateY(-2px);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #49b1f5, #00c4b6);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(73, 177, 245, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(73, 177, 245, 0.4);
}

/* 卡片悬浮效果 */
.card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  #greeting-message {
    top: 10px;
    right: 10px;
    font-size: 12px;
    padding: 8px 12px;
  }
  
  #performance-monitor {
    bottom: 10px;
    left: 10px;
    font-size: 11px;
    padding: 8px 12px;
  }
  
  #container {
    margin: 10px;
  }
}
EOF

echo "9. 创建自定义 JavaScript..."
cat > source/js/custom.js << 'EOF'
// 自定义 JavaScript - 参考 blog.fzero.me

// 页面加载进度条
function initLoadingBar() {
  const loadingBar = document.createElement('div');
  loadingBar.id = 'loading-bar';
  document.body.appendChild(loadingBar);
  
  let width = 0;
  const interval = setInterval(() => {
    width += Math.random() * 15;
    if (width >= 100) {
      width = 100;
      clearInterval(interval);
      setTimeout(() => {
        loadingBar.style.opacity = '0';
        setTimeout(() => loadingBar.remove(), 300);
      }, 200);
    }
    loadingBar.style.width = width + '%';
  }, 100);
}

// 问候语句
function showGreeting() {
  const greetings = [
    '早上好！欢迎来到 Stysky 的博客！',
    '下午好！今天过得怎么样？',
    '晚上好！夜深了，注意休息哦！',
    '凌晨好！这么晚还在学习，真棒！'
  ];
  
  const hour = new Date().getHours();
  let greeting;
  
  if (hour < 6) greeting = greetings[3];
  else if (hour < 12) greeting = greetings[0];
  else if (hour < 18) greeting = greetings[1];
  else greeting = greetings[2];
  
  const greetingElement = document.createElement('div');
  greetingElement.id = 'greeting-message';
  greetingElement.textContent = greeting;
  document.body.appendChild(greetingElement);
  
  setTimeout(() => {
    greetingElement.classList.add('show');
  }, 500);
  
  setTimeout(() => {
    greetingElement.classList.remove('show');
    setTimeout(() => {
      greetingElement.remove();
    }, 500);
  }, 4000);
}

// 性能监控
class PerformanceMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frames = 0;
    this.init();
  }
  
  init() {
    const monitor = document.createElement('div');
    monitor.id = 'performance-monitor';
    monitor.innerHTML = `
      <div class="monitor-item">
        <span class="monitor-label">FPS:</span>
        <span class="monitor-value" id="fpsValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">延迟:</span>
        <span class="monitor-value" id="latencyValue">-- ms</span>
      </div>
    `;
    document.body.appendChild(monitor);
    
    this.updateFPS();
    this.updateLatency();
  }
  
  updateFPS() {
    const now = performance.now();
    this.frames++;
    
    if (now >= this.lastTime + 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      const fpsElement = document.getElementById('fpsValue');
      if (fpsElement) {
        fpsElement.textContent = this.fps;
      }
      this.frames = 0;
      this.lastTime = now;
    }
    
    requestAnimationFrame(() => this.updateFPS());
  }
  
  updateLatency() {
    const start = performance.now();
    fetch('/')
      .then(() => {
        const latency = Math.round(performance.now() - start);
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) {
          latencyElement.textContent = latency + ' ms';
        }
      })
      .catch(() => {
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) {
          latencyElement.textContent = '-- ms';
        }
      });
    
    setTimeout(() => this.updateLatency(), 5000);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');
  
  // 初始化加载进度条
  initLoadingBar();
  
  // 显示问候语句（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(showGreeting, 1000);
  }
  
  // 初始化性能监控
  new PerformanceMonitor();
  
  // 添加页面切换动画
  document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.href && !e.target.href.includes('#')) {
      const link = e.target.href;
      if (link.startsWith(window.location.origin)) {
        e.preventDefault();
        
        // 显示加载动画
        const loadingBar = document.createElement('div');
        loadingBar.id = 'loading-bar';
        loadingBar.style.width = '0%';
        document.body.appendChild(loadingBar);
        
        let width = 0;
        const interval = setInterval(() => {
          width += 10;
          loadingBar.style.width = width + '%';
          if (width >= 100) {
            clearInterval(interval);
            window.location.href = link;
          }
        }, 50);
      }
    }
  });
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
  if (!document.hidden && window.location.pathname === '/') {
    setTimeout(showGreeting, 500);
  }
});

// 确保脚本在页面完全加载后执行
window.addEventListener('load', function() {
  console.log('页面完全加载完成');
});
EOF

echo "10. 检查背景图片..."
if [ -f "static/background.jpg" ]; then
    echo "✅ 背景图片存在: static/background.jpg"
else
    echo "⚠️  背景图片不存在，将使用默认背景"
    # 如果没有背景图片，创建一个占位符
    echo "请将您的背景图片命名为 background.jpg 并放在 static 文件夹中"
fi

echo "11. 重新生成静态文件..."
hexo clean
hexo generate

echo "12. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "13. 检查生成的文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件生成成功"
else
    echo "❌ 主页文件生成失败"
fi

if [ -f "public/css/custom.css" ]; then
    echo "✅ 自定义 CSS 部署成功"
else
    echo "❌ 自定义 CSS 部署失败"
fi

if [ -f "public/js/custom.js" ]; then
    echo "✅ 自定义 JavaScript 部署成功"
else
    echo "❌ 自定义 JavaScript 部署失败"
fi

echo "=========================================="
echo "修复和增强完成！"
echo "=========================================="
echo "✅ 独立按钮跳转页面 - 已配置"
echo "✅ 加载进度条 - 已添加"
echo "✅ 右上角问候语句 - 已实现"
echo "✅ 左下角性能监控 - 已添加"
echo "✅ 背景图片支持 - 已配置"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "测试页面："
echo "- 关于页面: https://www.stysky.cloud/about/"
echo "- 留言板: https://www.stysky.cloud/guestbook/"
echo "- 吐槽页面: https://www.stysky.cloud/shuoshuo/"
echo "=========================================="
echo "如果背景图片没有显示，请确保："
echo "1. 图片文件名为 background.jpg"
echo "2. 图片位于 static 文件夹中"
echo "3. 图片格式为 JPG/JPEG"
echo "==========================================" 