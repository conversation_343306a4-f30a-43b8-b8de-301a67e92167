#!/bin/bash

echo "=========================================="
echo "检查博客页面和功能状态..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查页面文件..."
echo "关于页面:"
if [ -f "source/about/index.md" ]; then
    echo "✅ 关于页面已创建"
else
    echo "❌ 关于页面未创建"
fi

echo "留言板页面:"
if [ -f "source/guestbook/index.md" ]; then
    echo "✅ 留言板页面已创建"
else
    echo "❌ 留言板页面未创建"
fi

echo "吐槽页面:"
if [ -f "source/shuoshuo/index.md" ]; then
    echo "✅ 吐槽页面已创建"
else
    echo "❌ 吐槽页面未创建"
fi

echo "2. 检查主题配置..."
if [ -f "themes/butterfly/_config.yml" ]; then
    echo "✅ Butterfly 主题配置存在"
else
    echo "❌ Butterfly 主题配置不存在"
fi

echo "3. 检查菜单配置..."
if grep -q "关于" _config.yml; then
    echo "✅ 菜单配置包含关于页面"
else
    echo "❌ 菜单配置缺少关于页面"
fi

if grep -q "留言板" _config.yml; then
    echo "✅ 菜单配置包含留言板"
else
    echo "❌ 菜单配置缺少留言板"
fi

if grep -q "吐槽" _config.yml; then
    echo "✅ 菜单配置包含吐槽页面"
else
    echo "❌ 菜单配置缺少吐槽页面"
fi

echo "4. 检查主题颜色配置..."
if grep -q "theme_color" themes/butterfly/_config.yml; then
    echo "✅ 主题颜色配置存在"
else
    echo "❌ 主题颜色配置不存在"
fi

echo "5. 检查侧边栏配置..."
if grep -q "card_author" themes/butterfly/_config.yml; then
    echo "✅ 侧边栏作者卡片配置存在"
else
    echo "❌ 侧边栏作者卡片配置不存在"
fi

echo "=========================================="
echo "检查完成！"
echo "==========================================" 