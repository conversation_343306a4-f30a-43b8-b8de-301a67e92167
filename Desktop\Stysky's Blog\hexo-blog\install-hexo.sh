#!/bin/bash

# Hexo 博客一键安装脚本
# 适用于宝塔面板环境

echo "=========================================="
echo "开始安装 Hexo 博客系统..."
echo "=========================================="

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 root 用户运行此脚本"
    exit 1
fi

# 检查 Node.js 是否已安装
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，正在安装..."
    # 安装 Node.js
    curl -fsSL https://deb.nodesource.com/setup_16.x | bash -
    apt-get install -y nodejs
else
    echo "Node.js 已安装，版本: $(node -v)"
fi

# 检查 Git 是否已安装
if ! command -v git &> /dev/null; then
    echo "Git 未安装，正在安装..."
    apt-get update
    apt-get install -y git
else
    echo "Git 已安装，版本: $(git --version)"
fi

# 全局安装 Hexo CLI
echo "正在安装 Hexo CLI..."
npm install -g hexo-cli

# 检查安装是否成功
if ! command -v hexo &> /dev/null; then
    echo "Hexo 安装失败，请检查网络连接"
    exit 1
else
    echo "Hexo 安装成功，版本: $(hexo version)"
fi

# 创建博客目录
BLOG_DIR="/www/wwwroot/hexo-blog"
echo "正在创建博客目录: $BLOG_DIR"

if [ ! -d "$BLOG_DIR" ]; then
    mkdir -p "$BLOG_DIR"
fi

cd "$BLOG_DIR"

# 初始化 Hexo 项目
echo "正在初始化 Hexo 项目..."
hexo init . --yes

# 安装基础依赖
echo "正在安装基础依赖..."
npm install

# 安装推荐插件
echo "正在安装推荐插件..."
npm install hexo-generator-search --save
npm install hexo-wordcount --save
npm install hexo-generator-feed --save
npm install hexo-generator-sitemap --save
npm install hexo-generator-baidu-sitemap --save
npm install hexo-blog-encrypt --save
npm install hexo-related-posts --save

# 安装 Butterfly 主题
echo "正在安装 Butterfly 主题..."
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save

# 设置目录权限
echo "正在设置目录权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

# 创建部署脚本
echo "正在创建部署脚本..."
cat > deploy.sh << 'EOF'
#!/bin/bash
cd /www/wwwroot/hexo-blog
hexo clean
hexo generate
echo "部署完成！"
EOF

chmod +x deploy.sh

# 生成示例文章
echo "正在创建示例文章..."
hexo new post "欢迎使用 Hexo 博客"

# 生成静态文件
echo "正在生成静态文件..."
hexo generate

echo "=========================================="
echo "Hexo 博客安装完成！"
echo "=========================================="
echo "博客目录: $BLOG_DIR"
echo "网站根目录: $BLOG_DIR/public"
echo "部署命令: ./deploy.sh"
echo "本地预览: hexo server"
echo "=========================================="
echo "下一步操作："
echo "1. 在宝塔面板中创建网站，根目录设置为: $BLOG_DIR/public"
echo "2. 编辑 _config.yml 配置文件"
echo "3. 编辑 themes/butterfly/_config.yml 主题配置"
echo "4. 运行 ./deploy.sh 部署网站"
echo "==========================================" 