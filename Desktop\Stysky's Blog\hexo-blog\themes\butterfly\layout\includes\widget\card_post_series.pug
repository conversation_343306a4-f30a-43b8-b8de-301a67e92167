if theme.aside.card_post_series.enable
  - const array = fragment_cache('seriesArr', groupPosts)
  .card-widget.card-post-series
    .item-headline
      i.fa-solid.fa-layer-group
      span= theme.aside.card_post_series.series_title ? page.series : _p('aside.card_post_series')
    .aside-list
      each item in array[page.series]
        - const { path, title = _p('no_title'), cover, cover_type, date:dateA } = item
        - let link = url_for(path)
        - let no_cover = cover === false || !theme.cover.aside_enable ? 'no-cover' : ''
        .aside-list-item(class=no_cover)
          if cover && theme.cover.aside_enable
            a.thumbnail(href=link title=title)
              if cover_type === 'img'
                img(src=url_for(cover) onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'` alt=title)
              else
                div(style=`background: ${cover}`)
          .content
            a.title(href=link title=title)= title
            time(datetime=date_xml(dateA) title=_p('post.created') + ' ' + full_date(dateA)) #[=date(dateA, config.date_format)]
