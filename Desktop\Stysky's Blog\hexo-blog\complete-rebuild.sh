#!/bin/bash

echo "=========================================="
echo "彻底重建 - 解决所有问题..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 备份重要文件..."
mkdir -p backup
cp -r static backup/
cp -r source/_posts backup/ 2>/dev/null || echo "没有文章文件需要备份"

echo "2. 删除源文件重新构建..."
rm -rf source/css source/js source/_data
rm -f themes/butterfly/_config.yml
rm -f _config.yml

echo "3. 重新创建主配置文件..."
cat > _config.yml << 'CONFIG_EOF'
# Hexo Configuration
title: <PERSON><PERSON><PERSON>'s Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
CONFIG_EOF

echo "4. 重新创建主题配置文件，修复YAML错误..."
cat > themes/butterfly/_config.yml << 'THEME_CONFIG_EOF'
# Butterfly 主题配置
title: Stysky's Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai

# 主题设置
theme: butterfly

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/starryskybyd || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope

# 头像设置
avatar:
  img: /static/user6.jpg
  effect: true

# 首页设置
index_img: /static/background.jpg
index_img_height: 100vh

# 搜索设置 - 修复YAML缩进
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 代码高亮
highlight_theme: normal
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 阅读模式
reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png

# 返回顶部
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

# 页脚设置
footer:
  owner:
    enable: true
    since: 2023
  custom_text: Hi, welcome to my <a href="https://github.com/starryskybyd">blog</a>!
  icon:
    name: fa fa-heart
    animated: true
    color: "#ff0000"

# 自定义注入配置
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>
THEME_CONFIG_EOF

echo "5. 创建自定义 CSS 目录和文件..."
mkdir -p source/css
cat > source/css/custom.css << 'CSS_EOF'
/* 彻底修复背景图片显示问题 */

/* 强制设置背景图片 */
html, body {
  background-image: url('/static/background.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
  background-color: transparent !important;
}

/* 移除所有可能覆盖背景的元素 */
#header, #header-wrap, #page-header, #site-header, .header,
.navbar, .navbar-default, .navbar-fixed-top,
#container, #main, .main, .content, .article-container,
.layout, .layout-wrap, .layout-container {
  background: transparent !important;
  background-color: transparent !important;
}

/* 导航栏样式 */
.navbar {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-links a, .navbar-nav a, .navbar-nav .nav-link {
  color: #fff !important;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 10px 16px;
  margin: 0 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-links a:hover, .navbar-nav a:hover, .navbar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 首页英雄区域 */
#page-header {
  background: transparent !important;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

#page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

#page-header .page-title {
  color: #fff;
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  z-index: 2;
  position: relative;
  margin-bottom: 1rem;
}

/* 文章卡片样式 */
.card, .article-card, .post-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 2rem;
}

.card:hover, .article-card:hover, .post-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* 内容区域样式 */
#container, #main, .main, .content, .article-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  margin: 2rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 按钮样式 */
.btn, .btn-primary, .btn-default, .button,
input[type="submit"], input[type="button"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  font-weight: 500;
}

.btn:hover, .btn-primary:hover, .btn-default:hover, .button:hover,
input[type="submit"]:hover, input[type="button"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* 加载进度条 */
#loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  z-index: 9999;
  transition: width 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.5);
}

/* 问候语句 */
#greeting-message {
  position: fixed;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  color: #333;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  z-index: 9998;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#greeting-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* 性能监控 */
#performance-monitor {
  position: fixed;
  bottom: 30px;
  left: 30px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 13px;
  z-index: 9997;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  align-items: center;
}

#performance-monitor .monitor-label {
  color: #ccc;
  font-weight: 500;
}

#performance-monitor .monitor-value {
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
}

/* IP坐标显示 */
#ip-location {
  position: fixed;
  top: 80px;
  right: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  color: #333;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 13px;
  z-index: 9996;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 300px;
}

#ip-location .location-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

#ip-location .location-icon {
  margin-right: 8px;
  color: #667eea;
}

/* 头像样式 */
.avatar-img {
  border-radius: 50%;
  transition: all 0.3s ease;
}

.avatar-img:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 标签样式 */
.tag-cloud a {
  display: inline-block;
  margin: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.3s ease;
}

.tag-cloud a:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 响应式优化 */
@media (max-width: 768px) {
  #page-header .page-title {
    font-size: 2.5rem;
  }
  
  #greeting-message, #ip-location {
    top: 20px;
    right: 20px;
    font-size: 12px;
    padding: 12px 16px;
  }
  
  #performance-monitor {
    bottom: 20px;
    left: 20px;
    font-size: 12px;
    padding: 12px 16px;
  }
  
  #container, #main, .main, .content, .article-container {
    margin: 1rem;
    padding: 1.5rem;
  }
}
CSS_EOF

echo "6. 创建自定义 JavaScript 目录和文件..."
mkdir -p source/js
cat > source/js/custom.js << 'JS_EOF'
// 自定义 JavaScript 功能

// 加载进度条
function initLoadingBar() {
  const loadingBar = document.createElement('div');
  loadingBar.id = 'loading-bar';
  document.body.appendChild(loadingBar);
  
  let width = 0;
  const interval = setInterval(() => {
    width += Math.random() * 20;
    if (width >= 100) {
      width = 100;
      clearInterval(interval);
      setTimeout(() => {
        loadingBar.style.opacity = '0';
        setTimeout(() => loadingBar.remove(), 300);
      }, 200);
    }
    loadingBar.style.width = width + '%';
  }, 100);
}

// 问候语句
function showGreeting() {
  const hour = new Date().getHours();
  let greeting;
  
  if (hour < 6) greeting = '🌙 凌晨好！欢迎来到 Stysky 的博客！';
  else if (hour < 12) greeting = '🌅 早上好！欢迎来到 Stysky 的博客！';
  else if (hour < 18) greeting = ☀️ 下午好！今天过得怎么样？';
  else greeting = '🌆 晚上好！夜深了，注意休息哦！';
  
  const greetingElement = document.createElement('div');
  greetingElement.id = 'greeting-message';
  greetingElement.textContent = greeting;
  document.body.appendChild(greetingElement);
  
  setTimeout(() => greetingElement.classList.add('show'), 500);
  setTimeout(() => {
    greetingElement.classList.remove('show');
    setTimeout(() => greetingElement.remove(), 500);
  }, 5000);
}

// IP坐标显示功能
function showIPLocation() {
  fetch('https://ipapi.co/json/')
    .then(response => response.json())
    .then(data => {
      const locationElement = document.createElement('div');
      locationElement.id = 'ip-location';
      locationElement.innerHTML = `
        <div class="location-item">
          <span class="location-icon">  </span>
          <span>欢迎来自${data.country_name}${data.region}${data.city}的小伙伴</span>
        </div>
        <div class="location-item">
          <span class="location-icon">  </span>
          <span>您的IP地址: ${data.ip}</span>
        </div>
        <div class="location-item">
          <span class="location-icon">  </span>
          <span>网络运营商: ${data.org || '未知'}</span>
        </div>
      `;
      document.body.appendChild(locationElement);
      
      // 5秒后自动隐藏
      setTimeout(() => {
        locationElement.style.opacity = '0';
        setTimeout(() => locationElement.remove(), 500);
      }, 5000);
    })
    .catch(error => {
      console.log('IP定位服务不可用:', error);
    });
}

// 性能监控
class PerformanceMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frames = 0;
    this.init();
  }
  
  init() {
    const monitor = document.createElement('div');
    monitor.id = 'performance-monitor';
    monitor.innerHTML = `
      <div class="monitor-item">
        <span class="monitor-label">FPS:</span>
        <span class="monitor-value" id="fpsValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">延迟:</span>
        <span class="monitor-value" id="latencyValue">-- ms</span>
      </div>
    `;
    document.body.appendChild(monitor);
    
    this.updateFPS();
    this.updateLatency();
  }
  
  updateFPS() {
    const now = performance.now();
    this.frames++;
    
    if (now >= this.lastTime + 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      const fpsElement = document.getElementById('fpsValue');
      if (fpsElement) fpsElement.textContent = this.fps;
      this.frames = 0;
      this.lastTime = now;
    }
    
    requestAnimationFrame(() => this.updateFPS());
  }
  
  updateLatency() {
    const start = performance.now();
    fetch('/')
      .then(() => {
        const latency = Math.round(performance.now() - start);
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = latency + ' ms';
      })
      .catch(() => {
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = '-- ms';
      });
    
    setTimeout(() => this.updateLatency(), 5000);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');
  
  // 初始化加载进度条
  initLoadingBar();
  
  // 显示问候语句（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(showGreeting, 1000);
    setTimeout(showIPLocation, 2000);
  }
  
  // 初始化性能监控
  new PerformanceMonitor();
  
  // 修复导航菜单显示
  setTimeout(() => {
    const navLinks = document.querySelectorAll('.nav-links a, .navbar-nav a, .navbar-nav .nav-link');
    navLinks.forEach(link => {
      link.style.display = 'inline-block';
      link.style.visibility = 'visible';
      link.style.opacity = '1';
    });
  }, 1000);
  
  // 强制设置背景图片
  setTimeout(() => {
    document.body.style.backgroundImage = 'url("/static/background.jpg")';
    document.body.style.backgroundSize = 'cover';
    document.body.style.backgroundPosition = 'center';
    document.body.style.backgroundAttachment = 'fixed';
    document.body.style.backgroundRepeat = 'no-repeat';
  }, 100);
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
  if (!document.hidden && window.location.pathname === '/') {
    setTimeout(showGreeting, 500);
  }
});
JS_EOF

echo "7. 创建标签页面..."
mkdir -p source/tags
cat > source/tags/index.md << 'EOF'
---
title: 标签
date: 2023-01-01 00:00:00
type: "tags"
---
