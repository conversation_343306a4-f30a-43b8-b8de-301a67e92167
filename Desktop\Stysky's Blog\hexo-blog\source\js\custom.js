// 自定义 JavaScript 功能

// 现代化加载进度条
function initLoadingBar() {
  // 创建加载容器
  const loadingContainer = document.createElement('div');
  loadingContainer.id = 'loading-container';
  document.body.appendChild(loadingContainer);

  // 创建进度条
  const loadingBar = document.createElement('div');
  loadingBar.id = 'loading-bar';
  loadingContainer.appendChild(loadingBar);

  // 创建状态指示器
  const loadingStatus = document.createElement('div');
  loadingStatus.id = 'loading-status';
  loadingStatus.textContent = '正在加载...';
  document.body.appendChild(loadingStatus);

  // 显示状态指示器
  setTimeout(() => loadingStatus.classList.add('show'), 100);

  let width = 0;
  let stage = 0;
  const stages = [
    { text: '正在加载资源...', target: 30 },
    { text: '正在渲染页面...', target: 60 },
    { text: '正在初始化组件...', target: 85 },
    { text: '即将完成...', target: 100 }
  ];

  const interval = setInterval(() => {
    const currentStage = stages[stage];
    const increment = Math.random() * 8 + 2;

    width = Math.min(width + increment, currentStage.target);
    loadingBar.style.width = width + '%';

    // 更新状态文本
    if (loadingStatus.textContent !== currentStage.text) {
      loadingStatus.textContent = currentStage.text;
    }

    // 检查是否需要进入下一阶段
    if (width >= currentStage.target && stage < stages.length - 1) {
      stage++;
    }

    // 完成加载
    if (width >= 100) {
      clearInterval(interval);
      loadingStatus.textContent = '加载完成！';

      setTimeout(() => {
        loadingBar.classList.add('complete');
        loadingStatus.classList.remove('show');
        loadingStatus.classList.add('hide');

        setTimeout(() => {
          loadingContainer.remove();
          loadingStatus.remove();
        }, 500);
      }, 300);
    }
  }, 80);

  // 确保在页面完全加载后完成进度条
  window.addEventListener('load', () => {
    if (width < 100) {
      const finalInterval = setInterval(() => {
        width += 5;
        if (width >= 100) {
          width = 100;
          clearInterval(finalInterval);
        }
        loadingBar.style.width = width + '%';
      }, 50);
    }
  });
}

// 聊天气泡式问候语
function showGreeting() {
  const hour = new Date().getHours();
  const now = new Date();
  const timeString = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

  let greeting, emoji;
  const greetings = [
    { condition: hour < 6, emoji: '🌙', text: '凌晨好！夜深了，还在学习吗？' },
    { condition: hour < 9, emoji: '🌅', text: '早上好！新的一天开始了！' },
    { condition: hour < 12, emoji: '☀️', text: '上午好！今天要加油哦！' },
    { condition: hour < 14, emoji: '🌞', text: '中午好！记得吃午饭哦！' },
    { condition: hour < 18, emoji: '🌤️', text: '下午好！今天过得怎么样？' },
    { condition: hour < 21, emoji: '🌆', text: '晚上好！辛苦了一天！' },
    { condition: true, emoji: '🌙', text: '夜深了，注意休息哦！' }
  ];

  const currentGreeting = greetings.find(g => g.condition);

  // 随机问候语变体
  const variations = [
    `${currentGreeting.emoji} ${currentGreeting.text}`,
    `${currentGreeting.emoji} 欢迎来到 Stysky 的博客！`,
    `${currentGreeting.emoji} 很高兴见到你！`,
    `${currentGreeting.emoji} 希望你在这里找到有用的内容！`,
    `${currentGreeting.emoji} 感谢你的访问！`
  ];

  const randomGreeting = variations[Math.floor(Math.random() * variations.length)];

  const greetingElement = document.createElement('div');
  greetingElement.id = 'greeting-message';
  greetingElement.innerHTML = `
    <button class="close-btn" onclick="this.parentElement.remove()">×</button>
    <div class="greeting-header">
      <div class="greeting-avatar">🤖</div>
      <div class="greeting-name">Stysky Bot</div>
      <div class="greeting-time">${timeString}</div>
    </div>
    <div class="greeting-text typing-effect">${randomGreeting}</div>
  `;
  document.body.appendChild(greetingElement);

  // 显示动画
  setTimeout(() => greetingElement.classList.add('show'), 300);

  // 打字机效果
  const textElement = greetingElement.querySelector('.typing-effect');
  const text = textElement.textContent;
  textElement.textContent = '';
  textElement.style.borderRight = '2px solid rgba(255, 255, 255, 0.7)';

  let i = 0;
  const typeInterval = setInterval(() => {
    textElement.textContent = text.slice(0, i + 1);
    i++;
    if (i >= text.length) {
      clearInterval(typeInterval);
      // 移除光标
      setTimeout(() => {
        textElement.style.borderRight = 'none';
      }, 1000);
    }
  }, 80);

  // 5秒后自动隐藏
  setTimeout(() => {
    greetingElement.classList.remove('show');
    setTimeout(() => {
      if (greetingElement.parentElement) {
        greetingElement.remove();
      }
    }, 600);
  }, 5000);
}

// IP地址信息显示功能 - 增强版
function showIPLocation() {
  // 尝试多个IP API服务
  const ipApis = [
    'https://ipapi.co/json/',
    'https://api.ipify.org?format=json',
    'https://httpbin.org/ip'
  ];

  async function fetchIPInfo() {
    for (const api of ipApis) {
      try {
        const response = await fetch(api);
        const data = await response.json();

        // 根据不同API调整数据格式
        let ipInfo = {};
        if (api.includes('ipapi.co')) {
          ipInfo = {
            ip: data.ip,
            country: data.country_name || '未知',
            region: data.region || '',
            city: data.city || '',
            org: data.org || '未知',
            timezone: data.timezone || '未知'
          };
        } else if (api.includes('ipify')) {
          ipInfo = {
            ip: data.ip,
            country: '未知',
            region: '',
            city: '',
            org: '未知',
            timezone: '未知'
          };
        }

        return ipInfo;
      } catch (error) {
        console.log(`API ${api} 不可用:`, error);
        continue;
      }
    }

    // 如果所有API都失败，返回默认信息
    return {
      ip: '获取失败',
      country: '未知',
      region: '',
      city: '',
      org: '未知',
      timezone: '未知'
    };
  }

  fetchIPInfo().then(data => {
    const locationElement = document.createElement('div');
    locationElement.id = 'ip-location';
    locationElement.innerHTML = `
      <button class="close-btn" onclick="this.parentElement.remove()">×</button>
      <div class="location-header">
        <span class="header-icon">🌐</span>
        <span class="header-title">访客信息</span>
      </div>
      <div class="location-item">
        <span class="location-icon">🌍</span>
        <span class="location-text">
          欢迎来自 <span class="location-value">${data.country}${data.region}${data.city}</span> 的小伙伴
        </span>
      </div>
      <div class="location-item">
        <span class="location-icon">📍</span>
        <span class="location-text">
          IP地址: <span class="location-value">${data.ip}</span>
        </span>
      </div>
      <div class="location-item">
        <span class="location-icon">🏢</span>
        <span class="location-text">
          网络运营商: <span class="location-value">${data.org}</span>
        </span>
      </div>
      <div class="location-item">
        <span class="location-icon">🕐</span>
        <span class="location-text">
          时区: <span class="location-value">${data.timezone}</span>
        </span>
      </div>
    `;
    document.body.appendChild(locationElement);

    // 显示动画
    setTimeout(() => locationElement.classList.add('show'), 100);

    // 8秒后自动隐藏
    setTimeout(() => {
      locationElement.classList.remove('show');
      setTimeout(() => {
        if (locationElement.parentElement) {
          locationElement.remove();
        }
      }, 500);
    }, 8000);
  });
}

// 性能监控 - 增强版
class PerformanceMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frames = 0;
    this.latency = 0;
    this.memoryUsage = 0;
    this.init();
  }

  init() {
    const monitor = document.createElement('div');
    monitor.id = 'performance-monitor';
    monitor.innerHTML = `
      <div class="monitor-header">
        <span class="header-icon">⚡</span>
        <span class="header-title">性能监控</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">
          🎯 FPS:
          <span class="monitor-indicator"></span>
        </span>
        <span class="monitor-value" id="fpsValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">
          🌐 延迟:
        </span>
        <span class="monitor-value" id="latencyValue">-- ms</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">
          💾 内存:
        </span>
        <span class="monitor-value" id="memoryValue">-- MB</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">
          ⏱️ 加载:
        </span>
        <span class="monitor-value" id="loadTimeValue">-- ms</span>
      </div>
    `;
    document.body.appendChild(monitor);

    this.updateFPS();
    this.updateLatency();
    this.updateMemoryUsage();
    this.updateLoadTime();
  }

  updateFPS() {
    const now = performance.now();
    this.frames++;

    if (now >= this.lastTime + 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      const fpsElement = document.getElementById('fpsValue');
      if (fpsElement) {
        fpsElement.textContent = this.fps;
        // 根据FPS设置颜色
        fpsElement.className = 'monitor-value ' + this.getPerformanceClass(this.fps, 60, 30);
      }
      this.frames = 0;
      this.lastTime = now;
    }

    requestAnimationFrame(() => this.updateFPS());
  }

  updateLatency() {
    const start = performance.now();
    const testUrl = window.location.origin + '/favicon.ico';

    fetch(testUrl, { method: 'HEAD', cache: 'no-cache' })
      .then(() => {
        this.latency = Math.round(performance.now() - start);
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) {
          latencyElement.textContent = this.latency + ' ms';
          // 根据延迟设置颜色 (越低越好)
          latencyElement.className = 'monitor-value ' + this.getPerformanceClass(200 - this.latency, 150, 50);
        }
      })
      .catch(() => {
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) {
          latencyElement.textContent = '-- ms';
          latencyElement.className = 'monitor-value bad';
        }
      });

    setTimeout(() => this.updateLatency(), 10000);
  }

  updateMemoryUsage() {
    if (performance.memory) {
      const memory = performance.memory;
      this.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      const memoryElement = document.getElementById('memoryValue');
      if (memoryElement) {
        memoryElement.textContent = this.memoryUsage + ' MB';
        // 根据内存使用量设置颜色
        memoryElement.className = 'monitor-value ' + this.getPerformanceClass(100 - this.memoryUsage, 70, 30);
      }
    }

    setTimeout(() => this.updateMemoryUsage(), 5000);
  }

  updateLoadTime() {
    if (performance.timing) {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      const loadTimeElement = document.getElementById('loadTimeValue');
      if (loadTimeElement && loadTime > 0) {
        loadTimeElement.textContent = loadTime + ' ms';
        // 根据加载时间设置颜色 (越低越好)
        loadTimeElement.className = 'monitor-value ' + this.getPerformanceClass(5000 - loadTime, 3000, 1000);
      }
    }
  }

  getPerformanceClass(value, goodThreshold, warningThreshold) {
    if (value >= goodThreshold) return 'good';
    if (value >= warningThreshold) return 'warning';
    return 'bad';
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');
  
  // 初始化加载进度条
  initLoadingBar();
  
  // 显示问候语句（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(showGreeting, 1000);
    setTimeout(showIPLocation, 2000);
  }
  
  // 初始化性能监控
  new PerformanceMonitor();
  
  // 修复导航菜单显示
  setTimeout(() => {
    const navLinks = document.querySelectorAll('.nav-links a, .navbar-nav a, .navbar-nav .nav-link');
    navLinks.forEach(link => {
      link.style.display = 'inline-block';
      link.style.visibility = 'visible';
      link.style.opacity = '1';
    });
  }, 1000);
  
  // 强制设置背景图片
  setTimeout(() => {
    document.body.style.backgroundImage = 'url("/static/1.jpg")';
    document.body.style.backgroundSize = 'cover';
    document.body.style.backgroundPosition = 'center';
    document.body.style.backgroundAttachment = 'fixed';
    document.body.style.backgroundRepeat = 'no-repeat';
  }, 100);
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
  if (!document.hidden && window.location.pathname === '/') {
    setTimeout(showGreeting, 500);
  }
});
