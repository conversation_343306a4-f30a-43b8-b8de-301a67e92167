# Hexo 博客在宝塔面板上的安装指南

## 1. 环境准备

### 安装 Node.js
1. 宝塔面板 → 软件商店 → 搜索"Node.js版本管理器" → 安装
2. 安装 Node.js 16.x 或更高版本

### 安装 Git
```bash
yum install git -y  # CentOS
# 或
apt-get install git -y  # Ubuntu
```

## 2. 安装 Hexo

### 全局安装 Hexo CLI
```bash
npm install -g hexo-cli
```

### 验证安装
```bash
hexo version
```

## 3. 创建 Hexo 项目

### 创建博客目录
```bash
cd /www/wwwroot/你的域名
# 或
mkdir /www/wwwroot/hexo-blog
cd /www/wwwroot/hexo-blog
```

### 初始化项目
```bash
hexo init .
npm install
```

## 4. 安装推荐插件

```bash
# 搜索功能
npm install hexo-generator-search --save

# 字数统计
npm install hexo-wordcount --save

# RSS 订阅
npm install hexo-generator-feed --save

# 站点地图
npm install hexo-generator-sitemap --save

# 百度站点地图
npm install hexo-generator-baidu-sitemap --save

# 文章加密
npm install hexo-blog-encrypt --save

# 相关文章推荐
npm install hexo-related-posts --save
```

## 5. 安装 Butterfly 主题

```bash
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save
```

## 6. 宝塔面板配置

### 创建网站
1. 宝塔面板 → 网站 → 添加站点
2. 域名：你的域名
3. 根目录：`/www/wwwroot/hexo-blog/public`

### 配置伪静态
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## 7. 部署命令

```bash
# 生成静态文件
hexo generate

# 启动本地服务器（开发用）
hexo server

# 清理缓存
hexo clean

# 新建文章
hexo new post "文章标题"
```

## 8. 常用命令

```bash
# 一键部署
hexo clean && hexo generate

# 新建页面
hexo new page "页面名称"

# 发布草稿
hexo publish "草稿文件名"
```

## 9. 故障排除

### 权限问题
```bash
chown -R www:www /www/wwwroot/hexo-blog
chmod -R 755 /www/wwwroot/hexo-blog
```

### Node.js 版本问题
```bash
node -v  # 检查版本
npm -v   # 检查版本
```

## 10. 自动化部署

创建 `deploy.sh`：
```bash
#!/bin/bash
cd /www/wwwroot/hexo-blog
hexo clean
hexo generate
```

设置权限：
```bash
chmod +x deploy.sh
```

## 11. 安全建议

1. 定期更新 Node.js 和 npm 包
2. 配置防火墙规则
3. 启用 WAF 防护
4. 定期备份数据

---

完成以上步骤后，您的 Hexo 博客就可以在宝塔面板上正常运行了！ 