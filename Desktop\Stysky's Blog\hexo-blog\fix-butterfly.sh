#!/bin/bash

echo "=========================================="
echo "修复 Butterfly 主题注入..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 删除不支持的注入方式..."
rm -f source/_data/inject.yml

echo "2. 在主题配置中添加注入..."
cat >> themes/butterfly/_config.yml << 'EOF'

# 自定义注入配置
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>
EOF

echo "3. 重新生成..."
hexo clean
hexo generate

echo "4. 检查注入结果..."
if [ -f "public/index.html" ]; then
    if grep -q "custom.css" public/index.html; then
        echo "✅ 自定义 CSS 已注入"
    else
        echo "❌ 自定义 CSS 未注入"
    fi
    
    if grep -q "custom.js" public/index.html; then
        echo "✅ 自定义 JavaScript 已注入"
    else
        echo "❌ 自定义 JavaScript 未注入"
    fi
fi

echo "5. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "修复完成！"
echo "==========================================" 