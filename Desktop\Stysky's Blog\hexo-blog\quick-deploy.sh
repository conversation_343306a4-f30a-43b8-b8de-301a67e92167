#!/bin/bash

echo "=========================================="
echo "快速部署 <PERSON><PERSON><PERSON>'s Blog..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 清理缓存..."
hexo clean

echo "2. 生成静态文件..."
hexo generate

echo "3. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "4. 检查文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 静态文件生成成功"
else
    echo "❌ 静态文件生成失败"
    exit 1
fi

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "功能包括："
echo "✅ 独立按钮跳转页面"
echo "✅ 加载进度条"
echo "✅ 右上角问候语句"
echo "✅ 左下角性能监控"
echo "==========================================" 