# Stysky's Blog 完整安装部署指南

## 📋 项目概述

基于您现有的 PHP 博客系统，结合 Hexo 框架和 Butterfly 主题，创建一个具有独特设计风格的现代化博客。

## 🚀 快速安装

### 1. 环境准备
```bash
# 在宝塔面板安装 Node.js 16.x
# 安装 Git
yum install git -y  # CentOS
apt-get install git -y  # Ubuntu
```

### 2. 安装 Hexo
```bash
npm install -g hexo-cli
hexo version  # 验证安装
```

### 3. 创建项目
```bash
cd /www/wwwroot/www.stysky.cloud
mkdir hexo-blog
cd hexo-blog
hexo init .
npm install
```

### 4. 安装插件
```bash
npm install hexo-generator-search hexo-wordcount hexo-generator-feed --save
npm install hexo-generator-sitemap hexo-blog-encrypt hexo-related-posts --save
```

### 5. 安装主题
```bash
git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
npm install hexo-renderer-pug hexo-renderer-stylus --save
```

## ⚙️ 配置博客

### 1. 主配置文件 `_config.yml`
```yaml
title: Stysky's Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
theme: butterfly

menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots
```

### 2. 主题配置 `themes/butterfly/_config.yml`
```yaml
# Stysky 定制风格
theme_color:
  enable: true
  main: "#2d8cf0"  # 深蓝色主色调
  paginator: "#19be6b"  # 绿色分页
  button_hover: "#ff9900"  # 橙色按钮

menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

social:
  github: https://github.com/stysky || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope

aside:
  enable: true
  card_author:
    enable: true
    description: 允许一切自然发生
  card_recent_post:
    enable: true
    limit: 5
  card_categories:
    enable: true
    limit: 8
  card_tags:
    enable: true
    limit: 40
  card_archives:
    enable: true
    limit: 8

toc:
  enable: true
  number: true

post:
  copyright:
    enable: true
    license: CC BY-NC-SA 4.0
  recommend:
    enable: true
    limit: 6

local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
```

## 📄 创建页面

### 1. 关于页面
```bash
hexo new page about
```

编辑 `source/about/index.md`：
```markdown
---
title: 关于
date: 2024-01-01 00:00:00
type: about
---

# 关于 Stysky

你好，我是 Stysky，一个热爱技术和生活的程序员。

## 🎯 博客定位

这个博客主要分享：
- 技术文章和教程
- 生活感悟和思考
- 项目经验和总结

## 🛠️ 技术栈

- **前端**: HTML, CSS, JavaScript, Vue.js
- **后端**: PHP, Node.js, Python
- **数据库**: MySQL, MongoDB
- **运维**: Linux, Docker

感谢您的访问！
```

### 2. 留言板页面
```bash
hexo new page guestbook
```

### 3. 吐槽页面
```bash
hexo new page shuoshuo
```

## 🌐 宝塔面板配置

### 1. 创建网站
- 域名：www.stysky.cloud
- 根目录：`/www/wwwroot/www.stysky.cloud/hexo-blog/public`

### 2. 配置伪静态
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

### 3. 启用 SSL 证书

## 📝 部署脚本

创建 `deploy.sh`：
```bash
#!/bin/bash
cd /www/wwwroot/www.stysky.cloud/hexo-blog
hexo clean
hexo generate
chown -R www:www .
chmod -R 755 .
echo "部署完成！"
```

设置权限：
```bash
chmod +x deploy.sh
```

## 🎨 文章模板

创建 `scaffolds/post.md`：
```markdown
---
title: {{ title }}
date: {{ date }}
categories: 
  - 技术
tags: 
  - 标签1
  - 标签2
description: 文章摘要
toc: true
cover: /images/post-cover.jpg
---

## 前言

在这里写文章的开头部分。

## 正文内容

### 1. 第一个章节

在这里写第一个章节的内容。

```bash
# 示例代码
echo "Hello World"
```

### 2. 第二个章节

在这里写第二个章节的内容。

> 💡 **提示**：这是一个引用块。

## 总结

在这里写文章的总结部分。

---

<div class="note success">
  <p>🎉 感谢您的阅读！</p>
</div>
```

## 📝 日常使用

### 新建文章
```bash
hexo new post "文章标题"
```

### 本地预览
```bash
hexo server
```

### 部署网站
```bash
./deploy.sh
```

## 🔧 故障排除

### 权限问题
```bash
chown -R www:www /www/wwwroot/www.stysky.cloud/hexo-blog
chmod -R 755 /www/wwwroot/www.stysky.cloud/hexo-blog
```

### 版本问题
```bash
node -v
npm -v
```

## 🎯 特色功能

1. **混合架构**：静态页面 + 动态功能
2. **独特设计**：深蓝色主色调，专业而现代
3. **丰富功能**：搜索、目录、推荐、RSS
4. **良好体验**：响应式设计，平滑动画

## 📞 技术支持

完成安装后，您的博客将具备：
- 现代化的设计风格
- 完整的博客功能
- 良好的用户体验
- 优秀的性能表现

---

**Stysky's Blog** - 允许一切自然发生 🚀 