#!/bin/bash

echo "=========================================="
echo "修复所有问题..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 重新创建自定义 CSS..."
cat > source/css/custom.css << 'CSS_EOF'
/* 完全移除蓝色背景 */
body {
  background-image: url('/static/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  min-height: 100vh;
  background-color: transparent !important;
}

/* 移除所有蓝色背景 */
#header, #header-wrap, #page-header, #site-header, .header, .navbar {
  background: transparent !important;
  background-color: transparent !important;
}

/* 修复导航菜单 */
.navbar {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9) !important;
}

.nav-links a, .navbar-nav a {
  color: #333 !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 修复按钮样式 */
.btn, .btn-primary {
  background: linear-gradient(135deg, #49b1f5, #00c4b6) !important;
  border: none !important;
  color: white !important;
}

/* 性能监控 */
#performance-monitor {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9997;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 120px;
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

#performance-monitor .monitor-label {
  color: #ccc;
}

#performance-monitor .monitor-value {
  color: #49b1f5;
  font-weight: bold;
}
CSS_EOF

echo "2. 重新生成..."
hexo clean
hexo generate

echo "3. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "修复完成！"
echo "=========================================="
