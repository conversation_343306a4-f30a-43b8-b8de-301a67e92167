#!/bin/bash

# 修复 403 Forbidden 错误脚本

echo "=========================================="
echo "开始修复 403 Forbidden 错误..."
echo "=========================================="

# 设置博客目录
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
PUBLIC_DIR="$BLOG_DIR/public"

echo "1. 检查目录是否存在..."
if [ ! -d "$BLOG_DIR" ]; then
    echo "错误：博客目录不存在: $BLOG_DIR"
    exit 1
fi

if [ ! -d "$PUBLIC_DIR" ]; then
    echo "错误：public 目录不存在: $PUBLIC_DIR"
    exit 1
fi

echo "2. 重新生成静态文件..."
cd "$BLOG_DIR"
hexo clean
hexo generate

echo "3. 检查生成的文件..."
if [ ! -f "$PUBLIC_DIR/index.html" ]; then
    echo "错误：index.html 文件不存在"
    exit 1
fi

echo "4. 设置正确的权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"
find "$PUBLIC_DIR" -type f -exec chmod 644 {} \;
find "$PUBLIC_DIR" -type d -exec chmod 755 {} \;

echo "5. 检查文件列表..."
echo "Public 目录内容："
ls -la "$PUBLIC_DIR"

echo "6. 重启 Nginx..."
systemctl restart nginx

echo "=========================================="
echo "修复完成！"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "如果仍有问题，请检查："
echo "1. 宝塔面板网站设置中的根目录"
echo "2. Nginx 错误日志"
echo "3. 文件权限"
echo "==========================================" 