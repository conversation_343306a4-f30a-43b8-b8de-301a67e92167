#!/bin/bash

echo "=========================================="
echo "修复 YAML 错误并完善设计..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 修复 YAML 配置错误..."
# 重新创建主题配置，修复第124行的缩进错误
cat > themes/butterfly/_config.yml << 'CONFIG_EOF'
# Butterfly 主题配置
title: Stysky's Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
keywords: [Hexo, 博客, 技术, 生活, 运维, 前端, 后端, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai

# 主题设置
theme: butterfly

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 首页设置 - 参考 blog.fzero.me
index_img: /static/background.jpg
index_img_height: 100vh

# 搜索设置 - 修复YAML缩进
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 自定义注入配置
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>
CONFIG_EOF

echo "2. 创建自定义 CSS - 参考 blog.fzero.me 设计..."
cat > source/css/custom.css << 'CSS_EOF'
/* 参考 blog.fzero.me 的设计风格 */

/* 全局背景设置 */
body {
  background-image: url('/static/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  min-height: 100vh;
  background-color: transparent !important;
}

/* 移除所有默认背景 */
#header, #header-wrap, #page-header, #site-header, .header,
.navbar, .navbar-default, .navbar-fixed-top,
#container, #main, .main, .content, .article-container {
  background: transparent !important;
  background-color: transparent !important;
}

/* 导航栏样式 - 毛玻璃效果 */
.navbar {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-links a, .navbar-nav a, .navbar-nav .nav-link {
  color: #fff !important;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 10px 16px;
  margin: 0 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-links a:hover, .navbar-nav a:hover, .navbar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 首页英雄区域 */
#page-header {
  background: transparent !important;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

#page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

#page-header .page-title {
  color: #fff;
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  z-index: 2;
  position: relative;
  margin-bottom: 1rem;
}

/* 文章卡片样式 */
.card, .article-card, .post-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 2rem;
}

.card:hover, .article-card:hover, .post-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* 内容区域样式 */
#container, #main, .main, .content, .article-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  margin: 2rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 按钮样式 */
.btn, .btn-primary, .btn-default, .button,
input[type="submit"], input[type="button"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  font-weight: 500;
}

.btn:hover, .btn-primary:hover, .btn-default:hover, .button:hover,
input[type="submit"]:hover, input[type="button"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* 加载进度条 */
#loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  z-index: 9999;
  transition: width 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.5);
}

/* 问候语句 */
#greeting-message {
  position: fixed;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  color: #333;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  z-index: 9998;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.5s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#greeting-message.show {
  opacity: 1;
  transform: translateY(0);
}

/* 性能监控 */
#performance-monitor {
  position: fixed;
  bottom: 30px;
  left: 30px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 13px;
  z-index: 9997;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  align-items: center;
}

#performance-monitor .monitor-label {
  color: #ccc;
  font-weight: 500;
}

#performance-monitor .monitor-value {
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
}
CSS_EOF

echo "3. 创建自定义 JavaScript..."
cat > source/js/custom.js << 'JS_EOF'
// 参考 blog.fzero.me 的功能实现

// 加载进度条
function initLoadingBar() {
  const loadingBar = document.createElement('div');
  loadingBar.id = 'loading-bar';
  document.body.appendChild(loadingBar);
  
  let width = 0;
  const interval = setInterval(() => {
    width += Math.random() * 20;
    if (width >= 100) {
      width = 100;
      clearInterval(interval);
      setTimeout(() => {
        loadingBar.style.opacity = '0';
        setTimeout(() => loadingBar.remove(), 300);
      }, 200);
    }
    loadingBar.style.width = width + '%';
  }, 100);
}

// 问候语句
function showGreeting() {
  const hour = new Date().getHours();
  let greeting;
  
  if (hour < 6) greeting = '🌙 凌晨好！欢迎来到 Stysky 的博客！';
  else if (hour < 12) greeting = '🌅 早上好！欢迎来到 Stysky 的博客！';
  else if (hour < 18) greeting = '☀️ 下午好！今天过得怎么样？';
  else greeting = '🌆 晚上好！夜深了，注意休息哦！';
  
  const greetingElement = document.createElement('div');
  greetingElement.id = 'greeting-message';
  greetingElement.textContent = greeting;
  document.body.appendChild(greetingElement);
  
  setTimeout(() => greetingElement.classList.add('show'), 500);
  setTimeout(() => {
    greetingElement.classList.remove('show');
    setTimeout(() => greetingElement.remove(), 500);
  }, 5000);
}

// 性能监控
class PerformanceMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frames = 0;
    this.init();
  }
  
  init() {
    const monitor = document.createElement('div');
    monitor.id = 'performance-monitor';
    monitor.innerHTML = `
      <div class="monitor-item">
        <span class="monitor-label">FPS:</span>
        <span class="monitor-value" id="fpsValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">延迟:</span>
        <span class="monitor-value" id="latencyValue">-- ms</span>
      </div>
    `;
    document.body.appendChild(monitor);
    
    this.updateFPS();
    this.updateLatency();
  }
  
  updateFPS() {
    const now = performance.now();
    this.frames++;
    
    if (now >= this.lastTime + 1000) {
      this.fps = Math.round((this.frames * 1000) / (now - this.lastTime));
      const fpsElement = document.getElementById('fpsValue');
      if (fpsElement) fpsElement.textContent = this.fps;
      this.frames = 0;
      this.lastTime = now;
    }
    
    requestAnimationFrame(() => this.updateFPS());
  }
  
  updateLatency() {
    const start = performance.now();
    fetch('/')
      .then(() => {
        const latency = Math.round(performance.now() - start);
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = latency + ' ms';
      })
      .catch(() => {
        const latencyElement = document.getElementById('latencyValue');
        if (latencyElement) latencyElement.textContent = '-- ms';
      });
    
    setTimeout(() => this.updateLatency(), 5000);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');
  
  // 初始化加载进度条
  initLoadingBar();
  
  // 显示问候语句（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(showGreeting, 1000);
  }
  
  // 初始化性能监控
  new PerformanceMonitor();
  
  // 修复导航菜单显示
  setTimeout(() => {
    const navLinks = document.querySelectorAll('.nav-links a, .navbar-nav a, .navbar-nav .nav-link');
    navLinks.forEach(link => {
      link.style.display = 'inline-block';
      link.style.visibility = 'visible';
      link.style.opacity = '1';
    });
  }, 1000);
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
  if (!document.hidden && window.location.pathname === '/') {
    setTimeout(showGreeting, 500);
  }
});
JS_EOF

echo "4. 检查背景图片..."
if [ -f "static/background.jpg" ]; then
    echo "✅ 背景图片存在: static/background.jpg"
    ls -lh static/background.jpg
else
    echo "⚠️  背景图片不存在，请将图片命名为 background.jpg 并放在 static 文件夹中"
    echo "建议使用类似 blog.fzero.me 的动漫风格背景图片"
fi

echo "5. 清理并重新生成..."
hexo clean
hexo generate

echo "6. 检查生成的文件..."
if [ -f "public/index.html" ]; then
    echo "✅ 主页文件生成成功"
    echo "检查注入结果:"
    if grep -q "custom.css" public/index.html; then
        echo "✅ 自定义 CSS 已注入"
    else
        echo "❌ 自定义 CSS 未注入"
    fi
    
    if grep -q "custom.js" public/index.html; then
        echo "✅ 自定义 JavaScript 已注入"
    else
        echo "❌ 自定义 JavaScript 未注入"
    fi
else
    echo "❌ 主页文件生成失败"
fi

echo "7. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "修复完成！"
echo "=========================================="
echo "修复内容："
echo "✅ 修复 YAML 配置错误"
echo "✅ 实现类似 blog.fzero.me 的设计"
echo "✅ 静态图片背景"
echo "✅ 毛玻璃效果导航栏"
echo "✅ 优雅的卡片设计"
echo "✅ 性能监控功能"
echo "✅ 问候语句功能"
echo "✅ 加载进度条"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "如果仍有问题，请："
echo "1. 强制刷新浏览器（Ctrl+F5）"
echo "2. 清除浏览器缓存"
echo "3. 检查浏览器控制台"
echo "4. 确保 static/background.jpg 文件存在"
echo "=========================================="
