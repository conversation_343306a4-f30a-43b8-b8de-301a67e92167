/* 彻底修复背景图片显示问题 */

/* 强制设置背景图片 */
html, body {
  background-image: url('/static/1.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
  background-color: transparent !important;
}

/* 移除所有可能覆盖背景的元素 */
#header, #header-wrap, #page-header, #site-header, .header,
.navbar, .navbar-default, .navbar-fixed-top,
#container, #main, .main, .content, .article-container,
.layout, .layout-wrap, .layout-container {
  background: transparent !important;
  background-color: transparent !important;
}

/* 确保页面内容不被固定导航栏遮挡 */
body {
  padding-top: 60px !important;
}

#page-header {
  margin-top: 0 !important;
}

/* 现代化导航栏设计 - 完全固定 */
#nav {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  height: 60px !important;
  z-index: 10000 !important;
  backdrop-filter: blur(20px) !important;
  background: rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
  padding: 0 2rem !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

#nav:hover {
  background: rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2) !important;
}

/* 导航栏内容布局 - 标题移到最左边 */
#nav #blog-info {
  order: -1 !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
}

#nav #menus {
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
}

/* 网站标题样式 */
.nav-site-title {
  color: #fff !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  text-decoration: none !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.nav-site-title:hover {
  color: #667eea !important;
  transform: scale(1.05) !important;
}

/* 菜单项样式 */
#menus .menus_items {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

#menus .menus_items .menus_item a {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  text-decoration: none !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 6px !important;
}

#menus .menus_items .menus_item a:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #fff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

/* 搜索按钮样式 */
#search-button {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20px !important;
  padding: 8px 16px !important;
  color: #fff !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
}

#search-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

/* 移动端菜单按钮 */
#toggle-menu {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  color: #fff !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  display: none !important;
}

#toggle-menu:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.05) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #nav #blog-info {
    margin-left: 1rem !important;
  }

  #nav #menus {
    margin-right: 1rem !important;
  }

  #toggle-menu {
    display: block !important;
  }

  #menus .menus_items {
    display: none !important;
  }

  #search-button {
    display: none !important;
  }
}

/* 确保菜单容器可见 */
.menu, .nav-menu, .navigation {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 侧边栏卡片重新排序 - 参考 https://blog.fzero.me/ */
#aside-content .sticky_layout {
  display: flex !important;
  flex-direction: column !important;
}

/* 作者信息卡片 - 置顶 */
#aside-content .card-info {
  order: 1 !important;
}

/* 公告栏 - 第二位 */
#aside-content .card-announcement {
  order: 2 !important;
}

/* 访客信息/网站信息 - 第三位（固定在公告栏和最新文章之间） */
#aside-content .card-webinfo {
  order: 3 !important;
  margin: 15px 0 !important;
}

/* 最新文章 - 第四位 */
#aside-content .card-recent-post {
  order: 4 !important;
}

/* 分类 - 第五位 */
#aside-content .card-categories {
  order: 5 !important;
}

/* 标签 - 第六位 */
#aside-content .card-tags {
  order: 6 !important;
}

/* 归档 - 第七位 */
#aside-content .card-archives {
  order: 7 !important;
}

/* 其他卡片 - 默认顺序 */
#aside-content .card-widget:not(.card-info):not(.card-announcement):not(.card-webinfo):not(.card-recent-post):not(.card-categories):not(.card-tags):not(.card-archives) {
  order: 8 !important;
}

/* 首页英雄区域 */
#page-header {
  background: transparent !important;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}

#page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

#page-header .page-title {
  color: #fff;
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  z-index: 2;
  position: relative;
  margin-bottom: 1rem;
}

/* 文章卡片样式 */
.card, .article-card, .post-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 2rem;
}

.card:hover, .article-card:hover, .post-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* 内容区域样式 */
#container, #main, .main, .content, .article-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  margin: 2rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 按钮样式 */
.btn, .btn-primary, .btn-default, .button,
input[type="submit"], input[type="button"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  font-weight: 500;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.btn:hover, .btn-primary:hover, .btn-default:hover, .button:hover,
input[type="submit"]:hover, input[type="button"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* 现代化加载进度条 */
#loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  z-index: 10000;
  transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.6), 0 0 40px rgba(102, 126, 234, 0.3);
  border-radius: 0 0 2px 0;
}

#loading-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 1.5s infinite;
}

#loading-bar.complete {
  transition: opacity 0.5s ease;
  opacity: 0;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 加载进度条容器 */
#loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 9999;
  backdrop-filter: blur(10px);
}

/* 加载状态指示器 */
#loading-status {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 10001;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
}

#loading-status.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

#loading-status.hide {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

/* 聊天气泡式问候语 */
#greeting-message {
  position: fixed;
  top: 80px;
  right: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 18px 24px;
  border-radius: 20px 20px 5px 20px;
  font-size: 15px;
  font-weight: 500;
  z-index: 9998;
  opacity: 0;
  transform: translateX(30px) scale(0.8);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  max-width: 300px;
  min-width: 200px;
  position: relative;
  backdrop-filter: blur(10px);
}

#greeting-message::before {
  content: '';
  position: absolute;
  top: 100%;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #667eea;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

#greeting-message::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 20px 20px 5px 20px;
  z-index: -1;
}

#greeting-message.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

#greeting-message .greeting-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.9;
}

#greeting-message .greeting-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

#greeting-message .greeting-name {
  font-weight: 600;
}

#greeting-message .greeting-time {
  margin-left: auto;
  font-size: 10px;
  opacity: 0.7;
}

#greeting-message .greeting-text {
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#greeting-message .close-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#greeting-message .close-btn:hover {
  background: rgba(255, 0, 0, 0.1);
  color: #ff4757;
  transform: scale(1.1);
}

/* 打字机效果 */
#greeting-message .typing-effect {
  overflow: hidden;
  border-right: 2px solid rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: rgba(255, 255, 255, 0.7); }
}

/* 性能监控 - 现代化设计 */
#performance-monitor {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85) 0%, rgba(30, 30, 30, 0.9) 100%);
  backdrop-filter: blur(25px);
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 11px;
  z-index: 9997;
  border: 2px solid rgba(255, 255, 255, 0.1);
  min-width: 150px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4), 0 3px 8px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  opacity: 0.8;
}

#performance-monitor:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.5), 0 6px 16px rgba(102, 126, 234, 0.3);
}

#performance-monitor .monitor-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#performance-monitor .monitor-header .header-icon {
  font-size: 14px;
  margin-right: 6px;
  color: #667eea;
}

#performance-monitor .monitor-header .header-title {
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#performance-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all 0.2s ease;
  border-left: 2px solid transparent;
}

#performance-monitor .monitor-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
  transform: translateX(2px);
}

#performance-monitor .monitor-item:last-child {
  margin-bottom: 0;
}

#performance-monitor .monitor-label {
  color: #ccc;
  font-weight: 500;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

#performance-monitor .monitor-value {
  color: #667eea;
  font-weight: bold;
  font-size: 11px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

#performance-monitor .monitor-value.good {
  color: #2ed573;
}

#performance-monitor .monitor-value.warning {
  color: #ffa502;
}

#performance-monitor .monitor-value.bad {
  color: #ff4757;
}

/* 性能指示器动画 */
#performance-monitor .monitor-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
  animation: pulse 2s infinite;
  margin-left: 8px;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
  100% { opacity: 1; transform: scale(1); }
}

/* IP地址信息显示 - 参考目标网站设计 */
#ip-location {
  position: fixed;
  top: 120px;
  right: 30px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.95) 100%);
  backdrop-filter: blur(25px);
  color: #2c3e50;
  padding: 20px 24px;
  border-radius: 16px;
  font-size: 14px;
  z-index: 9996;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(102, 126, 234, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  max-width: 320px;
  min-width: 280px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(20px);
}

#ip-location.show {
  opacity: 1;
  transform: translateX(0);
}

#ip-location:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2), 0 6px 16px rgba(102, 126, 234, 0.15);
}

#ip-location .location-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

#ip-location .location-header .header-icon {
  font-size: 20px;
  margin-right: 10px;
  color: #667eea;
}

#ip-location .location-header .header-title {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#ip-location .location-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

#ip-location .location-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
  transform: translateX(4px);
}

#ip-location .location-item:last-child {
  margin-bottom: 0;
}

#ip-location .location-icon {
  margin-right: 12px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

#ip-location .location-text {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

#ip-location .location-value {
  color: #667eea;
  font-weight: 600;
}

/* IP地址信息关闭按钮 */
#ip-location .close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  transition: all 0.2s ease;
}

#ip-location .close-btn:hover {
  background: rgba(255, 0, 0, 0.1);
  color: #ff4757;
  transform: scale(1.1);
}

/* 头像样式 */
.avatar-img {
  border-radius: 50%;
  transition: all 0.3s ease;
}

.avatar-img:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 标签样式 */
.tag-cloud a {
  display: inline-block;
  margin: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.3s ease;
}

.tag-cloud a:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 确保所有功能按钮可见 */
.menu-item, .nav-item, .nav-link, .navbar-brand,
.social-links a, .social-icons a,
.footer-links a, .sidebar-links a {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 修复可能的隐藏元素 */
.hidden, .invisible, .d-none {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  #page-header .page-title {
    font-size: 2.5rem;
  }
  
  #greeting-message, #ip-location {
    top: 20px;
    right: 20px;
    font-size: 12px;
    padding: 12px 16px;
  }
  
  #performance-monitor {
    bottom: 20px;
    left: 20px;
    font-size: 12px;
    padding: 12px 16px;
  }
  
  #container, #main, .main, .content, .article-container {
    margin: 1rem;
    padding: 1.5rem;
  }
} 