#!/bin/bash

# 配置 Butterfly 主题脚本

echo "=========================================="
echo "开始配置 Butterfly 主题..."
echo "=========================================="

# 设置博客目录
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查 Butterfly 主题是否已安装..."
if [ ! -d "themes/butterfly" ]; then
    echo "Butterfly 主题未安装，正在安装..."
    git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
    npm install hexo-renderer-pug hexo-renderer-stylus --save
else
    echo "Butterfly 主题已存在"
fi

echo "2. 配置主配置文件..."
cat > _config.yml << 'EOF'
# Hexo Configuration
title: <PERSON><PERSON><PERSON>'s Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
keywords: [Hexo, 博客, 技术, 生活, 运维, 前端, 后端, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 分类与标签
category_dir: categories
tag_dir: tags
archive_dir: archives

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
EOF

echo "3. 配置 Butterfly 主题..."
cat > themes/butterfly/_config.yml << 'EOF'
# Butterfly 主题配置 - Stysky 定制版

# 主题颜色配置（Stysky 风格）
theme_color:
  enable: true
  main: "#2d8cf0"  # 主色调：深蓝色
  paginator: "#19be6b"  # 分页：绿色
  button_hover: "#ff9900"  # 按钮悬停：橙色
  text_selection: "#19be6b"  # 文本选择：绿色
  link_color: "#515a6e"  # 链接：深灰色
  meta_color: "#808695"  # 元信息：中灰色
  hr_color: "#e8eaec"  # 分割线：浅灰色
  code_color: "#f47466"  # 代码：红色
  code_bg_color: "#f8f8f8"  # 代码背景：浅灰
  toc_color: "#19be6b"  # 目录：绿色
  blockquote_padding_color: "#2d8cf0"  # 引用：蓝色
  blockquote_background_color: "#f8f9fa"  # 引用背景：浅灰

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/stysky || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope
  twitter: https://twitter.com/stysky || fab fa-twitter

# 侧边栏配置
aside:
  enable: true
  hide: false
  button: true
  mobile: true
  position: right
  display:
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: 允许一切自然发生
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me
      link: https://github.com/stysky
  card_announcement:
    enable: true
    content: 欢迎来到 Stysky's Blog！
  card_recent_post:
    enable: true
    limit: 5
    sort: date
    sort_order: -1
  card_categories:
    enable: true
    limit: 8
    sort: name
    sort_order: 1
  card_tags:
    enable: true
    limit: 40
    sort: name
    sort_order: 1
    color: false
    initialize: false
  card_archives:
    enable: true
    type: monthly
    format: MMMM YYYY
    order: -1
    limit: 8
    sort: date
    sort_order: -1
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: true

# 文章目录
toc:
  enable: true
  number: true
  expand: false
  style_simple: false

# 代码高亮
highlight_theme: mac
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 文章配置
post:
  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

  # 文章推荐
  recommend:
    enable: true
    limit: 6
    date: 90

  # 文章目录
  toc:
    enable: true
    number: true
    expand: false
    style_simple: false

# 搜索配置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 页面配置
page:
  # 关于页面
  about:
    enable: true
    avatar: /images/avatar.jpg
    name: Stysky
    subtitle: 允许一切自然发生
    type: self
    comment: true
    social:
      github: https://github.com/stysky
      email: mailto:<EMAIL>
      twitter: https://twitter.com/stysky

  # 留言板页面
  guestbook:
    enable: true
    comment: true

  # 吐槽页面
  shuoshuo:
    enable: true
    comment: true

# 动画效果
animate:
  enable: true
  title: true
  sidebar: true
  post_list: true
  post_item: true
  post_header: true
  post_body: true
  post_footer: true
  comment: true

# 特效配置
pjax:
  enable: true
  exclude:
    - /music/
    - /js/
    - /css/
    - /img/

# 统计配置
statistics:
  enable: true
  busuanzi:
    enable: true
    total_visitors: true
    total_views: true
    post_views: true

# 其他配置
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png
EOF

echo "4. 创建页面..."
hexo new page about
hexo new page guestbook
hexo new page shuoshuo

echo "5. 编辑关于页面..."
cat > source/about/index.md << 'EOF'
---
title: 关于
date: 2024-01-01 00:00:00
type: about
---

# 关于 Stysky

## 👨‍💻 个人简介

你好，我是 Stysky，一个热爱技术和生活的程序员。

## 🎯 博客定位

这个博客主要分享：
- 技术文章和教程
- 生活感悟和思考
- 项目经验和总结
- 学习笔记和心得

## 🛠️ 技术栈

- **前端**: HTML, CSS, JavaScript, Vue.js, React
- **后端**: PHP, Node.js, Python
- **数据库**: MySQL, MongoDB, Redis
- **运维**: Linux, Docker, Kubernetes
- **其他**: Git, Docker, CI/CD

## 📫 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [@stysky](https://github.com/stysky)
- **Twitter**: [@stysky](https://twitter.com/stysky)

## 🎨 博客特色

- 简洁优雅的设计风格
- 响应式布局，支持多设备
- 丰富的交互功能
- 良好的阅读体验

---

感谢您的访问！如果觉得文章有帮助，请点个赞支持一下。
EOF

echo "6. 编辑留言板页面..."
cat > source/guestbook/index.md << 'EOF'
---
title: 留言板
date: 2024-01-01 00:00:00
type: guestbook
---

# 留言板

欢迎在这里留言交流！

## 💬 留言规则

1. 文明发言，互相尊重
2. 可以讨论技术问题
3. 可以分享生活感悟
4. 禁止恶意攻击和广告

## 🎯 留言主题

- 技术交流
- 生活分享
- 建议反馈
- 合作洽谈

---

期待您的留言！
EOF

echo "7. 编辑吐槽页面..."
cat > source/shuoshuo/index.md << 'EOF'
---
title: 日常吐槽
date: 2024-01-01 00:00:00
type: shuoshuo
---

# 日常吐槽

这里记录一些日常的碎碎念...

## 📝 吐槽分类

- 技术相关
- 生活感悟
- 工作心得
- 学习笔记

---

生活不易，吐槽有益！
EOF

echo "8. 创建文章模板..."
mkdir -p scaffolds
cat > scaffolds/post.md << 'EOF'
---
title: {{ title }}
date: {{ date }}
updated: {{ date }}
categories: 
  - 技术
tags: 
  - 标签1
  - 标签2
description: 文章摘要，请在此处填写文章的主要内容和亮点
keywords: 
  - 关键词1
  - 关键词2
  - 关键词3
toc: true
toc_number: true
toc_style_simple: false
copyright: true
mathjax: false
comments: true
top_img: /images/post-bg.jpg
cover: /images/post-cover.jpg
abbrlink: 
reprint_policy: cc_by
headimg: /images/post-head.jpg
---

<!-- 文章头部信息 -->
<div class="note info">
  <p>📝 本文发布于 {{ date }}，最后更新于 {{ date }}</p>
</div>

<!-- 文章目录 -->
<div class="note warning">
  <p>📋 文章目录</p>
</div>

<!-- 文章内容 -->
## 前言

在这里写文章的开头部分，简要介绍文章的主题和内容。

## 正文内容

### 1. 第一个章节

在这里写第一个章节的内容。

```bash
# 示例代码
echo "Hello World"
```

### 2. 第二个章节

在这里写第二个章节的内容。

> 💡 **提示**：这是一个引用块，可以用来突出重要信息。

### 3. 总结

在这里写文章的总结部分。

## 参考资料

- [参考链接1](https://example.com)
- [参考链接2](https://example.com)

## 相关文章

<!-- 这里会自动显示相关文章 -->

---

<div class="note success">
  <p>🎉 感谢您的阅读！如果觉得文章有帮助，请点个赞支持一下。</p>
</div>
EOF

echo "9. 重新生成静态文件..."
hexo clean
hexo generate

echo "10. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "Butterfly 主题配置完成！"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "您将看到："
echo "- 深蓝色主色调的设计"
echo "- 完整的导航菜单"
echo "- 侧边栏功能"
echo "- 关于、留言板、吐槽页面"
echo "==========================================" 