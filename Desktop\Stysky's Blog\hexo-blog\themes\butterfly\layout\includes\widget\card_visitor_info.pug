// 访客信息卡片 - 简化版本，只显示基本信息
.card-widget.card-visitor-info
  .card-content
    .visitor-info-header
      i.fas.fa-globe-asia
      span.visitor-info-title 访客信息
    .visitor-info-content
      .visitor-location
        span.location-greeting 欢迎来自
        span.location-place#visitor-location China Guangdong Guangzhou
        span.location-suffix 的小伙伴
      .visitor-details
        .detail-item
          span.detail-label IP地址:
          span.detail-value#visitor-ip *************
        .detail-item
          span.detail-label 网络运营商:
          span.detail-value#visitor-isp China Mobile Communications Group Co., Ltd.
        .detail-item
          span.detail-label 时区:
          span.detail-value#visitor-timezone Asia/Shanghai

script.
  // 访客信息获取脚本
  (function() {
    // 更新时间显示
    function updateTime() {
      const now = new Date();
      const timeString = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
      });
      const timeElement = document.getElementById('visitor-time');
      if (timeElement) {
        timeElement.textContent = timeString;
      }
    }
    
    // 每秒更新时间
    setInterval(updateTime, 1000);
    updateTime();
    
    // 获取访客IP信息
    async function getVisitorInfo() {
      try {
        // 尝试多个IP API
        const apis = [
          'https://ipapi.co/json/',
          'https://api.ipify.org?format=json',
          'https://httpbin.org/ip'
        ];
        
        for (const api of apis) {
          try {
            const response = await fetch(api);
            const data = await response.json();
            
            let ipInfo = {};
            if (api.includes('ipapi.co')) {
              ipInfo = {
                ip: data.ip || '未知',
                country: data.country_name || '未知',
                region: data.region || '',
                city: data.city || '',
                org: data.org || '未知运营商'
              };
            } else if (api.includes('ipify')) {
              ipInfo = {
                ip: data.ip || '未知',
                country: '未知',
                region: '',
                city: '',
                org: '未知运营商'
              };
            } else if (api.includes('httpbin')) {
              ipInfo = {
                ip: data.origin || '未知',
                country: '未知',
                region: '',
                city: '',
                org: '未知运营商'
              };
            }
            
            // 更新页面显示
            updateVisitorDisplay(ipInfo);
            return;
          } catch (error) {
            console.log(`API ${api} 不可用:`, error);
            continue;
          }
        }
        
        // 所有API都失败时的默认显示
        updateVisitorDisplay({
          ip: '获取失败',
          country: '未知',
          region: '',
          city: '',
          org: '未知运营商'
        });
        
      } catch (error) {
        console.error('获取访客信息失败:', error);
        updateVisitorDisplay({
          ip: '获取失败',
          country: '未知',
          region: '',
          city: '',
          org: '未知运营商'
        });
      }
    }
    
    // 更新访客信息显示
    function updateVisitorDisplay(info) {
      // 更新位置信息
      const locationElement = document.getElementById('visitor-location');
      if (locationElement) {
        let locationText = '';
        if (info.country && info.country !== '未知') {
          locationText = info.country;
          if (info.region) locationText += ' ' + info.region;
          if (info.city) locationText += ' ' + info.city;
        } else {
          locationText = '未知地区';
        }
        locationElement.textContent = locationText;
      }
      
      // 更新IP地址
      const ipElement = document.getElementById('visitor-ip');
      if (ipElement) {
        ipElement.textContent = info.ip;
      }
      
      // 更新运营商
      const ispElement = document.getElementById('visitor-isp');
      if (ispElement) {
        ispElement.textContent = info.org;
      }
    }
    
    // 模拟访问统计（实际项目中应该连接后端API）
    function updateVisitStats() {
      const todayElement = document.getElementById('today-visits');
      const totalElement = document.getElementById('total-visitors');
      
      if (todayElement) {
        // 从localStorage获取或生成今日访问数
        let todayVisits = localStorage.getItem('todayVisits') || Math.floor(Math.random() * 50) + 10;
        todayElement.textContent = todayVisits;
      }
      
      if (totalElement) {
        // 从localStorage获取或生成总访客数
        let totalVisitors = localStorage.getItem('totalVisitors') || Math.floor(Math.random() * 1000) + 500;
        totalElement.textContent = totalVisitors;
      }
    }
    
    // 页面加载完成后获取信息
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(getVisitorInfo, 1000);
        updateVisitStats();
      });
    } else {
      setTimeout(getVisitorInfo, 1000);
      updateVisitStats();
    }
  })();
