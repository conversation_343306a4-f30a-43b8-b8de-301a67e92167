script.
  (() => {
    function loadValine () {
      function initValine () {
        let initData = {
          el: '#vcomment',
          appId: '#{theme.valine.appId}',
          appKey: '#{theme.valine.appKey}',
          serverURLs: '#{theme.valine.serverURLs}'
        }
        
        const valine = new Valine(initData)
      }

      if (typeof Valine === 'function') initValine() 
      else btf.getScript('!{url_for(theme.asset.valine)}').then(initValine)
    }

    window.pjax ? loadValine() : window.addEventListener('load', loadValine)
  })()
