#!/bin/bash

echo "=========================================="
echo "部署博客更新 - 三项功能优化"
echo "1. 导航栏标题固定到左上角"
echo "2. 导航栏固定在顶部"
echo "3. IP地址信息移到侧边栏"
echo "=========================================="

# 设置博客目录
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 清理缓存..."
npx hexo clean

echo "2. 生成静态文件..."
npx hexo generate

echo "3. 检查生成结果..."
if [ -f "public/index.html" ]; then
    echo "✅ 静态文件生成成功"
else
    echo "❌ 静态文件生成失败"
    exit 1
fi

echo "4. 检查自定义文件..."
if [ -f "public/css/custom.css" ]; then
    echo "✅ 自定义CSS文件存在"
else
    echo "❌ 自定义CSS文件缺失"
fi

if [ -f "public/js/custom.js" ]; then
    echo "✅ 自定义JS文件存在"
else
    echo "❌ 自定义JS文件缺失"
fi

echo "5. 重启Nginx（如果需要）..."
# nginx -s reload

echo "=========================================="
echo "部署完成！"
echo "请访问 https://www.stysky.cloud 查看效果"
echo "=========================================="

echo "更新内容："
echo "✅ 博客标题已固定到导航栏左上角"
echo "✅ 导航栏已固定在页面顶部"
echo "✅ IP地址信息已移到侧边栏（个人简介下方）"
echo "✅ 访客信息卡片包含位置、IP、运营商、时间等信息"
echo "✅ 响应式设计适配移动端"
