.limit-one-line
  overflow: hidden
  text-overflow: ellipsis
  white-space: nowrap

.limit-more-line
  display: -webkit-box
  overflow: hidden
  -webkit-box-orient: vertical

.fontawesomeIcon
  display: inline-block
  font-weight: 600
  font-family: 'Font Awesome 7 Free', 'Font Awesome 6 Free'
  text-rendering: auto
  -webkit-font-smoothing: antialiased

addBorderRadius(x = 6, hide = false)
  if hexo-config('rounded_corners_ui')
    border-radius: unit(x, 'px')

    if hide
      overflow: hidden

// card hover
.cardHover
  background: var(--card-bg)
  box-shadow: var(--card-box-shadow)
  transition: all .3s
  addBorderRadius(8)

  &:hover
    box-shadow: var(--card-hover-box-shadow)

.imgHover
  width: 100%
  height: 100%
  transition: filter 375ms ease-in .2s, transform .6s
  object-fit: cover

  &:hover
    transform: scale(1.1)

.postImgHover
  &:hover
    .cover
      opacity: .5
      transform: scale(1.1)

  .cover
    width: 100%
    height: 100%
    opacity: .4
    transition: all .6s, filter 375ms ease-in .2s
    object-fit: cover

.list-beauty
  list-style: none

  li
    position: relative
    padding: .12em .4em .12em 1.4em

    &:hover
      &:before
        border-color: var(--pseudo-hover)

    &:before
      position: absolute
      top: .67em
      left: 0
      width: w = .43em
      height: h = w
      border: .5 * w solid $light-blue
      border-radius: w
      background: transparent
      content: ''
      cursor: pointer
      transition: all .3s ease-out

.custom-hr
  position: relative
  margin: 40px auto
  border: 2px dashed var(--hr-border)

  if hexo-config('hr_icon.enable')
    width: calc(100% - 4px)

    &:hover
      &:before
        left: calc(95% - 20px)

    &:before
      position: absolute
      top: $hr-icon-top
      left: 5%
      z-index: 1
      color: var(--hr-before-color)
      content: $hr-icon
      font-size: 20px
      line-height: 1
      transition: all 1s ease-in-out
      @extend .fontawesomeIcon

.verticalCenter
  position: absolute
  top: 50%
  width: 100%
  transform: translate(0, -50%)

maxWidth600()
  @media screen and (max-width: 600px)
    {block}

maxWidth768()
  @media screen and (max-width: 768px)
    {block}

minWidth768()
  @media screen and (min-width: 768px)
    {block}

maxWidth1024()
  @media screen and (max-width: 1024px)
    {block}

minWidth1024()
  @media screen and (min-width: 1024px)
    {block}

maxWidth900()
  @media screen and (max-width: 900px)
    {block}

minWidth901()
  @media screen and (min-width: 901px)
    {block}

minWidth900()
  @media screen and (min-width: 900px)
    {block}

minWidth2000()
  @media screen and (min-width: 2000px)
    {block}

// animation
if hexo-config('enter_transitions')
  #content-inner,
  #footer
    animation: bottom-top 1s

  #page-header:not(.full_page),
  #nav.show
    animation: header-effect 1s

  #site-title,
  #site-subtitle
    animation: titleScale 1s

  canvas:not(#ribbon-canvas),
  #web_bg
    animation: to_show 4s

  #ribbon-canvas
    animation: ribbon_to_show 4s

  #sidebar-menus
    &.open
      for i in 1 2 3 4
        > :nth-child({i})
          animation: sidebarItem (i / 5s)

.scroll-down-effects
  animation: scroll-down-effect 1.5s infinite

if hexo-config('avatar.effect') == true
  .avatar-img
    animation: avatar_turn_around 2s linear infinite

.reward-main
  animation: donate_effcet .3s .1s ease both

@keyframes scroll-down-effect
  0%
    opacity: .4
    transform: translate(0, 0)

  50%
    opacity: 1
    transform: translate(0, -16px)

  100%
    opacity: .4
    transform: translate(0, 0)

@keyframes header-effect
  0%
    transform: translateY(-35px)

  100%
    transform: translateY(0)

@keyframes bottom-top
  0%
    transform: translateY(35px)

  100%
    transform: translateY(0)

@keyframes titleScale
  0%
    opacity: 0
    transform: scale(.7)

  100%
    opacity: 1
    transform: scale(1)

@keyframes search_close
  0%
    opacity: 1
    transform: scale(1)

  100%
    opacity: 0
    transform: scale(.7)

@keyframes to_show
  0%
    opacity: 0

  100%
    opacity: 1

@keyframes to_hide
  0%
    opacity: 1

  100%
    opacity: 0

@keyframes ribbon_to_show
  0%
    opacity: 0

  100%
    opacity: hexo-config('canvas_ribbon.alpha')

@keyframes avatar_turn_around
  from
    transform: rotate(0)

  to
    transform: rotate(360deg)

@keyframes sub_menus
  0%
    opacity: 0
    transform: translateY(10px)

  100%
    opacity: 1
    transform: translateY(0)

@keyframes donate_effcet
  0%
    opacity: 0
    transform: translateY(-20px)

  100%
    opacity: 1
    transform: translateY(0)

@keyframes sidebarItem
  0%
    transform: translateX(200px)

  100%
    transform: translateX(0)
