# 博客三项功能优化 - 宝塔面板操作指南

## 更新内容概述

本次更新实现了以下三个功能，参考了 https://blog.fzero.me/ 的设计：

1. **博客标题固定到左上角** - 将"<PERSON><PERSON><PERSON>'s Blog"标题固定在导航栏左上角
2. **导航栏固定在顶部** - 导航栏不再跟随页面滚动，始终固定在顶部
3. **IP地址信息移到侧边栏** - 将访客IP信息从右上角移到侧边栏个人简介下方

## 宝塔面板操作步骤

### 第一步：上传文件到服务器

1. 登录宝塔面板
2. 进入文件管理
3. 导航到 `/www/wwwroot/www.stysky.cloud/hexo-blog/`
4. 将本地的以下文件上传到对应目录：

```
themes/butterfly/_config.yml
themes/butterfly/layout/includes/widget/card_visitor_info.pug
themes/butterfly/layout/includes/widget/index.pug
source/css/custom.css
source/js/custom.js
deploy-blog-updates.sh
```

### 第二步：设置文件权限

1. 在宝塔面板文件管理中，右键点击 `deploy-blog-updates.sh`
2. 选择"权限"，设置为 755
3. 确保脚本可执行

### 第三步：执行部署脚本

1. 在宝塔面板中打开"终端"
2. 切换到博客目录：
   ```bash
   cd /www/wwwroot/www.stysky.cloud/hexo-blog
   ```
3. 执行部署脚本：
   ```bash
   ./deploy-blog-updates.sh
   ```

### 第四步：手动执行（如果脚本失败）

如果脚本执行失败，可以手动执行以下命令：

```bash
# 切换到博客目录
cd /www/wwwroot/www.stysky.cloud/hexo-blog

# 清理缓存
npx hexo clean

# 生成静态文件
npx hexo generate

# 检查生成结果
ls -la public/
```

### 第五步：验证更新效果

访问 https://www.stysky.cloud 检查以下功能：

1. **导航栏固定** - 滚动页面时导航栏应该保持在顶部
2. **标题位置** - "Stysky's Blog"应该显示在导航栏左上角
3. **侧边栏访客信息** - 在右侧边栏个人简介下方应该看到新的访客信息卡片

## 功能详细说明

### 1. 导航栏固定功能

- 导航栏现在使用 `position: fixed` 固定在页面顶部
- 添加了毛玻璃效果和阴影
- 在 butterfly 主题配置中启用了 `nav.fixed: true`

### 2. 博客标题位置

- 标题"Stysky's Blog"现在固定在导航栏左上角
- 使用绝对定位确保位置固定
- 添加了悬停效果和颜色变化

### 3. 访客信息卡片

新的访客信息卡片包含：
- 访客地理位置（国家、省份、城市）
- IP地址显示
- 网络运营商信息
- 实时时间显示
- 访问统计（今日访问、总访客数）

## 故障排除

### 如果页面显示异常：

1. **检查文件是否正确上传**
   ```bash
   ls -la themes/butterfly/layout/includes/widget/card_visitor_info.pug
   ```

2. **检查CSS和JS文件**
   ```bash
   ls -la source/css/custom.css
   ls -la source/js/custom.js
   ```

3. **重新生成静态文件**
   ```bash
   npx hexo clean
   npx hexo generate
   ```

4. **检查Nginx配置**
   - 确保静态文件路径正确
   - 重启Nginx服务

### 如果访客信息不显示：

1. 检查浏览器控制台是否有JavaScript错误
2. 确认网络连接正常（IP API需要外网访问）
3. 检查CSP（内容安全策略）设置

## 备份说明

在执行更新前，建议备份以下文件：
- `themes/butterfly/_config.yml`
- `source/css/custom.css`
- `source/js/custom.js`

## 联系支持

如果遇到问题，请检查：
1. 宝塔面板错误日志
2. Nginx错误日志
3. 浏览器开发者工具控制台

更新完成后，您的博客将具有更现代化的导航栏设计和更丰富的访客信息展示功能！
