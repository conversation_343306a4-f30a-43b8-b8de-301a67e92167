- const { serverURL, option, pageview } = theme.waline
- const { lazyload, count, use } = theme.comments

script.
  (() => {
    let initFn = window.walineFn || null
    const isShuoshuo = GLOBAL_CONFIG_SITE.pageType === 'shuoshuo'
    const option = !{JSON.stringify(option)}

    const destroyWaline = ele => ele.destroy()

    const initWaline = (Fn, el = document, path = window.location.pathname) => {
      const waline = Fn({
        el: el.querySelector('#waline-wrap'),
        serverURL: '!{serverURL}',
        pageview: !{lazyload ? false : pageview},
        dark: 'html[data-theme="dark"]',
        comment: !{lazyload ? false : count},
        ...option,
        path: isShuoshuo ? path : (option && option.path) || path
      })

      if (isShuoshuo) {
        window.shuoshuoComment.destroyWaline = () => {
          destroyWaline(waline)
          if (el.children.length) {
            el.innerHTML = ''
            el.classList.add('no-comment')
          }
        }
      }
    }

    const loadWaline = (el, path) => {
      if (initFn) initWaline(initFn, el, path)
      else {
        btf.getCSS('!{url_for(theme.asset.waline_css)}')
          .then(() => import('!{url_for(theme.asset.waline_js)}'))
          .then(({ init }) => {
            initFn = init || Waline.init
            initWaline(initFn, el, path)
            window.walineFn = initFn
          })
      }
    }

    if (isShuoshuo) {
      '!{use[0]}' === 'Waline'
        ? window.shuoshuoComment = { loadComment: loadWaline } 
        : window.loadOtherComment = loadWaline
      return
    }

    if ('!{use[0]}' === 'Waline' || !!{lazyload}) {
      if (!{lazyload}) btf.loadComment(document.getElementById('waline-wrap'),loadWaline)
      else setTimeout(loadWaline, 0)
    } else {
      window.loadOtherComment = loadWaline
    }
  })()

