#!/bin/bash

echo "=========================================="
echo "直接修复注入问题..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 检查 Butterfly 主题模板..."
if [ -f "themes/butterfly/layout/includes/head.pug" ]; then
    echo "✅ 找到 head.pug 文件"
    
    echo "2. 备份原文件..."
    cp themes/butterfly/layout/includes/head.pug themes/butterfly/layout/includes/head.pug.backup
    
    echo "3. 在 head.pug 中添加自定义 CSS..."
    # 在 head.pug 末尾添加自定义 CSS 引用
    echo "link(rel='stylesheet' href='/css/custom.css')" >> themes/butterfly/layout/includes/head.pug
    
    echo "✅ 已添加自定义 CSS 引用到 head.pug"
else
    echo "❌ 未找到 head.pug 文件"
fi

echo "4. 检查并修改 footer 或 body 结束标签..."
if [ -f "themes/butterfly/layout/includes/footer.pug" ]; then
    echo "✅ 找到 footer.pug 文件"
    
    echo "5. 备份原文件..."
    cp themes/butterfly/layout/includes/footer.pug themes/butterfly/layout/includes/footer.pug.backup
    
    echo "6. 在 footer.pug 中添加自定义 JS..."
    # 在 footer.pug 末尾添加自定义 JS 引用
    echo "script(src='/js/custom.js')" >> themes/butterfly/layout/includes/footer.pug
    
    echo "✅ 已添加自定义 JS 引用到 footer.pug"
else
    echo "❌ 未找到 footer.pug 文件"
fi

echo "7. 重新生成..."
hexo clean
hexo generate

echo "8. 检查注入结果..."
if [ -f "public/index.html" ]; then
    if grep -q "custom.css" public/index.html; then
        echo "✅ 自定义 CSS 已注入"
    else
        echo "❌ 自定义 CSS 未注入"
    fi
    
    if grep -q "custom.js" public/index.html; then
        echo "✅ 自定义 JavaScript 已注入"
    else
        echo "❌ 自定义 JavaScript 未注入"
    fi
fi

echo "9. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "直接修复完成！"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "==========================================" 