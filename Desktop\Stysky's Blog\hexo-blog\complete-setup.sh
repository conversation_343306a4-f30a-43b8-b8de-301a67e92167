#!/bin/bash

echo "=========================================="
echo "完整配置 Stysky's Blog..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 确保 Butterfly 主题已安装..."
if [ ! -d "themes/butterfly" ]; then
    echo "安装 Butterfly 主题..."
    git clone -b master https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly
    npm install hexo-renderer-pug hexo-renderer-stylus --save
else
    echo "Butterfly 主题已存在"
fi

echo "2. 创建页面..."
hexo new page about
hexo new page guestbook
hexo new page shuoshuo

echo "3. 配置关于页面..."
cat > source/about/index.md << 'EOF'
---
title: 关于
date: 2024-01-01 00:00:00
type: about
---

# 关于 Stysky

## 👨‍💻 个人简介

你好，我是 Stysky，一个热爱技术和生活的程序员。

## 🎯 博客定位

这个博客主要分享：
- 技术文章和教程
- 生活感悟和思考
- 项目经验和总结
- 学习笔记和心得

## 🛠️ 技术栈

- **前端**: HTML, CSS, JavaScript, Vue.js, React
- **后端**: PHP, Node.js, Python
- **数据库**: MySQL, MongoDB, Redis
- **运维**: Linux, Docker, Kubernetes
- **其他**: Git, Docker, CI/CD

## 📫 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [@stysky](https://github.com/stysky)
- **Twitter**: [@stysky](https://twitter.com/stysky)

## 🎨 博客特色

- 简洁优雅的设计风格
- 响应式布局，支持多设备
- 丰富的交互功能
- 良好的阅读体验

---

感谢您的访问！如果觉得文章有帮助，请点个赞支持一下。
EOF

echo "4. 配置留言板页面..."
cat > source/guestbook/index.md << 'EOF'
---
title: 留言板
date: 2024-01-01 00:00:00
type: guestbook
---

# 留言板

欢迎在这里留言交流！

## 💬 留言规则

1. 文明发言，互相尊重
2. 可以讨论技术问题
3. 可以分享生活感悟
4. 禁止恶意攻击和广告

## 🎯 留言主题

- 技术交流
- 生活分享
- 建议反馈
- 合作洽谈

---

期待您的留言！
EOF

echo "5. 配置吐槽页面..."
cat > source/shuoshuo/index.md << 'EOF'
---
title: 日常吐槽
date: 2024-01-01 00:00:00
type: shuoshuo
---

# 日常吐槽

这里记录一些日常的碎碎念...

## 📝 吐槽分类

- 技术相关
- 生活感悟
- 工作心得
- 学习笔记

---

生活不易，吐槽有益！
EOF

echo "6. 配置 Butterfly 主题..."
cat > themes/butterfly/_config.yml << 'EOF'
# Butterfly 主题配置 - Stysky 定制版

# 主题颜色配置（Stysky 风格）
theme_color:
  enable: true
  main: "#2d8cf0"  # 主色调：深蓝色
  paginator: "#19be6b"  # 分页：绿色
  button_hover: "#ff9900"  # 按钮悬停：橙色
  text_selection: "#19be6b"  # 文本选择：绿色
  link_color: "#515a6e"  # 链接：深灰色
  meta_color: "#808695"  # 元信息：中灰色
  hr_color: "#e8eaec"  # 分割线：浅灰色
  code_color: "#f47466"  # 代码：红色
  code_bg_color: "#f8f8f8"  # 代码背景：浅灰
  toc_color: "#19be6b"  # 目录：绿色
  blockquote_padding_color: "#2d8cf0"  # 引用：蓝色
  blockquote_background_color: "#f8f9fa"  # 引用背景：浅灰

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/stysky || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope
  twitter: https://twitter.com/stysky || fab fa-twitter

# 侧边栏配置
aside:
  enable: true
  hide: false
  button: true
  mobile: true
  position: right
  display:
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: 允许一切自然发生
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me
      link: https://github.com/stysky
  card_announcement:
    enable: true
    content: 欢迎来到 Stysky's Blog！
  card_recent_post:
    enable: true
    limit: 5
    sort: date
    sort_order: -1
  card_categories:
    enable: true
    limit: 8
    sort: name
    sort_order: 1
  card_tags:
    enable: true
    limit: 40
    sort: name
    sort_order: 1
    color: false
    initialize: false
  card_archives:
    enable: true
    type: monthly
    format: MMMM YYYY
    order: -1
    limit: 8
    sort: date
    sort_order: -1
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: true

# 文章目录
toc:
  enable: true
  number: true
  expand: false
  style_simple: false

# 代码高亮
highlight_theme: mac
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 文章配置
post:
  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

  # 文章推荐
  recommend:
    enable: true
    limit: 6
    date: 90

  # 文章目录
  toc:
    enable: true
    number: true
    expand: false
    style_simple: false

# 搜索配置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 页面配置
page:
  # 关于页面
  about:
    enable: true
    avatar: /images/avatar.jpg
    name: Stysky
    subtitle: 允许一切自然发生
    type: self
    comment: true
    social:
      github: https://github.com/stysky
      email: mailto:<EMAIL>
      twitter: https://twitter.com/stysky

  # 留言板页面
  guestbook:
    enable: true
    comment: true

  # 吐槽页面
  shuoshuo:
    enable: true
    comment: true

# 动画效果
animate:
  enable: true
  title: true
  sidebar: true
  post_list: true
  post_item: true
  post_header: true
  post_body: true
  post_footer: true
  comment: true

# 特效配置
pjax:
  enable: true
  exclude:
    - /music/
    - /js/
    - /css/
    - /img/

# 统计配置
statistics:
  enable: true
  busuanzi:
    enable: true
    total_visitors: true
    total_views: true
    post_views: true

# 其他配置
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png
EOF

echo "7. 重新生成静态文件..."
hexo clean
hexo generate

echo "8. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "完整配置完成！"
echo "=========================================="
echo "现在您的博客包含以下功能："
echo "✅ Butterfly 主题 - 现代化设计"
echo "✅ 深蓝色主色调 - 专业风格"
echo "✅ 完整导航菜单 - 首页、分类、标签、归档、关于、留言板、吐槽"
echo "✅ 侧边栏功能 - 博主信息、最新文章、分类、标签、归档、网站信息"
echo "✅ 搜索功能 - 本地搜索"
echo "✅ 文章目录 - 自动生成"
echo "✅ 统计功能 - 访问量统计"
echo "✅ 响应式设计 - 移动端适配"
echo "✅ 动画效果 - 丰富的交互"
echo "=========================================="
echo "请访问: https://www.stysky.cloud"
echo "测试页面："
echo "- 关于页面: https://www.stysky.cloud/about/"
echo "- 留言板: https://www.stysky.cloud/guestbook/"
echo "- 吐槽页面: https://www.stysky.cloud/shuoshuo/"
echo "==========================================" 