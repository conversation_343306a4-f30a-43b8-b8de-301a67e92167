#!/bin/bash

echo "=========================================="
echo "彻底修复 - 解决所有问题..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 重新创建主配置文件..."
cat > _config.yml << 'CONFIG_EOF'
# Hexo Configuration
title: Stysky's Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
CONFIG_EOF

echo "2. 重新创建主题配置文件，修复YAML错误..."
cat > themes/butterfly/_config.yml << 'THEME_CONFIG_EOF'
# Butterfly 主题配置
title: Stysky's Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai

# 主题设置
theme: butterfly

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/starryskybyd || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope

# 头像设置
avatar:
  img: /static/user6.jpg
  effect: true

# 首页设置
index_img: /static/background.jpg
index_img_height: 100vh

# 搜索设置 - 修复YAML缩进
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 代码高亮
highlight_theme: normal
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 阅读模式
reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png

# 返回顶部
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

# 页脚设置
footer:
  owner:
    enable: true
    since: 2023
  custom_text: Hi, welcome to my <a href="https://github.com/starryskybyd">blog</a>!
  icon:
    name: fa fa-heart
    animated: true
    color: "#ff0000"

# 自定义注入配置
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>
THEME_CONFIG_EOF

echo "3. 创建五个文章分类页面..."
mkdir -p source/categories/linux-os
cat > source/categories/linux-os/index.md << 'EOF'
---
title: Linux操作系统
date: 2023-01-01 00:00:00
type: "categories"
---
