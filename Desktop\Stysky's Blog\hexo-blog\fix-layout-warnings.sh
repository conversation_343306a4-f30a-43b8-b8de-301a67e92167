#!/bin/bash

echo "=========================================="
echo "修复布局警告和创建缺失的页面..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 创建缺失的页面文件..."

# 创建关于页面
mkdir -p source/about
cat > source/about/index.md << 'EOF'
---
title: 关于
date: 2024-01-01 00:00:00
type: "about"
layout: "about"
---

# 关于 Stysky

## 个人简介

你好！我是 Stysky，一名热爱技术的博主。

## 技术栈

- **操作系统**: Linux (CentOS, Ubuntu)
- **编程语言**: Python, Shell, JavaScript
- **数据库**: MySQL, Redis
- **运维工具**: Docker, An<PERSON>, Jenkins
- **安全工具**: Nmap, Metasploit, Burp Suite

## 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [starryskybyd](https://github.com/starryskybyd)
- **博客**: https://www.stysky.cloud

## 博客分类

- **Linux操作系统**: 系统管理、配置优化
- **渗透测试技术**: 安全测试、漏洞分析
- **安全运维技术**: 运维自动化、安全防护
- **实战学习分享**: 项目经验、学习心得
- **生活随笔**: 日常感悟、生活记录

---

欢迎来到我的博客，一起学习成长！
EOF

# 创建留言板页面
mkdir -p source/guestbook
cat > source/guestbook/index.md << 'EOF'
---
title: 留言板
date: 2024-01-01 00:00:00
type: "guestbook"
layout: "guestbook"
---

# 留言板

欢迎在这里留下您的评论和建议！

## 留言规则

1. 请文明发言，互相尊重
2. 可以讨论技术问题、分享经验
3. 禁止发布垃圾信息或恶意内容

---

期待您的留言！
EOF

# 创建吐槽页面
mkdir -p source/shuoshuo
cat > source/shuoshuo/index.md << 'EOF'
---
title: 吐槽
date: 2024-01-01 00:00:00
type: "shuoshuo"
layout: "shuoshuo"
---

# 吐槽专区

这里是我的日常吐槽和碎碎念...

## 今日吐槽

- 代码写得好累，但是很有成就感！
- 学习新技术总是让人兴奋
- 生活不易，技术更不易

---

欢迎分享您的想法！
EOF

# 创建分类页面
mkdir -p source/categories
cat > source/categories/index.md << 'EOF'
---
title: 分类
date: 2024-01-01 00:00:00
type: "categories"
layout: "categories"
---

# 文章分类

## 分类列表

- **Linux操作系统**: 系统管理、配置优化
- **渗透测试技术**: 安全测试、漏洞分析  
- **安全运维技术**: 运维自动化、安全防护
- **实战学习分享**: 项目经验、学习心得
- **生活随笔**: 日常感悟、生活记录

---

点击分类查看相关文章！
EOF

# 创建标签页面
mkdir -p source/tags
cat > source/tags/index.md << 'EOF'
---
title: 标签
date: 2024-01-01 00:00:00
type: "tags"
layout: "tags"
---

# 文章标签

## 标签云

- **技术**: 技术相关文章
- **博客**: 博客相关文章
- **运维**: 运维相关文章
- **Linux**: Linux系统相关
- **渗透**: 渗透测试相关
- **网安**: 网络安全相关
- **MYSQL**: 数据库相关

---

点击标签查看相关文章！
EOF

echo "2. 创建Butterfly主题的布局文件..."

# 创建about布局
mkdir -p themes/butterfly/layout
cat > themes/butterfly/layout/about.ejs << 'EOF'
<%- include('_partial/header') %>

<div class="layout_page">
  <div class="layout_page_content">
    <div class="article-container">
      <div class="article-content">
        <%- page.content %>
      </div>
    </div>
  </div>
</div>

<%- include('_partial/footer') %>
EOF

# 创建guestbook布局
cat > themes/butterfly/layout/guestbook.ejs << 'EOF'
<%- include('_partial/header') %>

<div class="layout_page">
  <div class="layout_page_content">
    <div class="article-container">
      <div class="article-content">
        <%- page.content %>
      </div>
    </div>
  </div>
</div>

<%- include('_partial/footer') %>
EOF

# 创建shuoshuo布局
cat > themes/butterfly/layout/shuoshuo.ejs << 'EOF'
<%- include('_partial/header') %>

<div class="layout_page">
  <div class="layout_page_content">
    <div class="article-container">
      <div class="article-content">
        <%- page.content %>
      </div>
    </div>
  </div>
</div>

<%- include('_partial/footer') %>
EOF

# 创建categories布局
cat > themes/butterfly/layout/categories.ejs << 'EOF'
<%- include('_partial/header') %>

<div class="layout_page">
  <div class="layout_page_content">
    <div class="article-container">
      <div class="article-content">
        <%- page.content %>
      </div>
    </div>
  </div>
</div>

<%- include('_partial/footer') %>
EOF

# 创建tags布局
cat > themes/butterfly/layout/tags.ejs << 'EOF'
<%- include('_partial/header') %>

<div class="layout_page">
  <div class="layout_page_content">
    <div class="article-container">
      <div class="article-content">
        <%- page.content %>
      </div>
    </div>
  </div>
</div>

<%- include('_partial/footer') %>
EOF

echo "3. 更新主题配置文件，添加布局支持..."
cat >> themes/butterfly/_config.yml << 'EOF'

# 页面布局设置
default_layout: post
layout: post

# 自定义页面类型
page_types:
  about: about
  guestbook: guestbook
  shuoshuo: shuoshuo
  categories: categories
  tags: tags
EOF

echo "4. 创建示例文章..."
mkdir -p source/_posts
cat > source/_posts/hello-world.md << 'EOF'
---
title: 欢迎来到 Stysky 的博客
date: 2024-01-01 12:00:00
updated: 2024-01-01 12:00:00
categories:
  - 技术
tags:
  - 技术
  - 博客
  - 运维
description: 欢迎来到我的博客，这里将分享技术、生活和学习心得
keywords:
  - 技术
  - 博客
  - 运维
toc: true
toc_number: true
toc_style_simple: false
copyright: true
mathjax: false
comments: true
top_img: /static/background.jpg
cover: /static/background.jpg
abbrlink:
reprint_policy: cc_by
headimg: /static/user6.jpg
---

<!-- 文章头部信息 -->
<div class="note info">
  <p>📝 本文发布于 2024-01-01，最后更新于 2024-01-01</p>
</div>

<!-- 文章目录 -->
<div class="note warning">
  <p>📋 文章目录</p>
</div>

## 前言

欢迎来到 Stysky 的博客！这里将分享我在技术学习、运维实践、网络安全等方面的经验和心得。

## 博客特色

### 1. 技术分享
- Linux 系统管理
- 网络安全技术
- 运维自动化
- 数据库管理

### 2. 学习心得
- 技术学习笔记
- 项目实战经验
- 问题解决方案

### 3. 生活随笔
- 日常感悟
- 学习心得
- 生活记录

## 技术栈

- **操作系统**: Linux (CentOS, Ubuntu)
- **编程语言**: Python, Shell, JavaScript
- **数据库**: MySQL, Redis
- **运维工具**: Docker, Ansible, Jenkins
- **安全工具**: Nmap, Metasploit, Burp Suite

## 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/starryskybyd
- **博客**: https://www.stysky.cloud

## 相关文章

<!-- 这里会自动显示相关文章 -->

---

<div class="note success">
  <p>🎉 感谢您的阅读！如果觉得文章有帮助，请点个赞支持一下。</p>
</div>
EOF

echo "5. 创建分类和标签的示例文章..."

# Linux操作系统分类文章
cat > source/_posts/linux-system-management.md << 'EOF'
---
title: Linux系统管理基础
date: 2024-01-02 10:00:00
categories:
  - Linux操作系统
tags:
  - Linux
  - 系统管理
  - 运维
---

# Linux系统管理基础

## 系统概述

Linux是一个强大的开源操作系统，广泛应用于服务器环境。

## 常用命令

### 文件管理
```bash
ls -la          # 列出文件
cp file1 file2  # 复制文件
mv file1 file2  # 移动文件
rm file         # 删除文件
```

### 系统管理
```bash
ps aux          # 查看进程
top             # 系统监控
df -h           # 磁盘使用
free -h         # 内存使用
```

## 总结

掌握Linux系统管理是运维工程师的基本技能。
EOF

# 渗透测试技术分类文章
cat > source/_posts/penetration-testing-basics.md << 'EOF'
---
title: 渗透测试技术基础
date: 2024-01-03 14:00:00
categories:
  - 渗透测试技术
tags:
  - 渗透测试
  - 网络安全
  - 安全技术
---

# 渗透测试技术基础

## 什么是渗透测试

渗透测试是一种通过模拟攻击来评估系统安全性的方法。

## 测试流程

1. **信息收集**
2. **漏洞扫描**
3. **漏洞利用**
4. **权限提升**
5. **报告编写**

## 常用工具

- **Nmap**: 端口扫描
- **Metasploit**: 漏洞利用框架
- **Burp Suite**: Web应用测试
- **Wireshark**: 网络分析

## 注意事项

渗透测试必须在授权范围内进行，遵守相关法律法规。
EOF

# 安全运维技术分类文章
cat > source/_posts/security-operations.md << 'EOF'
---
title: 安全运维技术实践
date: 2024-01-04 16:00:00
categories:
  - 安全运维技术
tags:
  - 安全运维
  - 网络安全
  - 运维
---

# 安全运维技术实践

## 安全运维概述

安全运维是将安全理念融入日常运维工作的实践。

## 安全措施

### 1. 访问控制
- 最小权限原则
- 多因素认证
- 定期密码更新

### 2. 监控告警
- 日志监控
- 异常检测
- 实时告警

### 3. 备份恢复
- 定期备份
- 异地存储
- 恢复测试

## 最佳实践

1. 定期安全评估
2. 及时更新补丁
3. 员工安全培训
4. 应急响应预案

## 总结

安全运维是保障系统稳定运行的重要保障。
EOF

# 实战学习分享分类文章
cat > source/_posts/practical-learning-experience.md << 'EOF'
---
title: 实战学习经验分享
date: 2024-01-05 09:00:00
categories:
  - 实战学习分享
tags:
  - 学习经验
  - 实战分享
  - 技术学习
---

# 实战学习经验分享

## 学习心得

### 1. 理论与实践结合
- 理论学习是基础
- 实践操作是关键
- 项目实战是检验

### 2. 持续学习
- 技术更新很快
- 保持学习热情
- 关注行业动态

### 3. 记录总结
- 做好学习笔记
- 总结实践经验
- 分享技术心得

## 推荐学习方法

1. **项目驱动**: 通过实际项目学习
2. **问题导向**: 解决实际问题
3. **社区交流**: 参与技术社区
4. **文档阅读**: 阅读官方文档

## 学习资源

- 官方文档
- 技术博客
- 在线课程
- 开源项目

## 总结

学习是一个持续的过程，保持热情和耐心很重要。
EOF

# 生活随笔分类文章
cat > source/_posts/life-essay-first.md << 'EOF'
---
title: 生活随笔 - 技术人的日常
date: 2024-01-06 20:00:00
categories:
  - 生活随笔
tags:
  - 生活
  - 随笔
  - 感悟
---

# 技术人的日常

## 工作日常

### 早晨
- 检查系统状态
- 查看监控告警
- 处理紧急问题

### 白天
- 日常运维工作
- 技术学习研究
- 项目开发调试

### 晚上
- 总结一天工作
- 学习新技术
- 写技术博客

## 生活感悟

### 1. 工作与生活平衡
技术工作虽然忙碌，但也要注意休息和放松。

### 2. 持续学习
技术发展很快，需要不断学习新知识。

### 3. 分享交流
通过博客分享经验，与同行交流学习。

## 未来规划

1. 深入学习云原生技术
2. 提升安全技能
3. 参与开源项目
4. 写更多技术文章

## 总结

技术人的日常虽然忙碌，但充满挑战和乐趣。
EOF

echo "6. 清理并重新生成..."
npx hexo clean
npx hexo generate

echo "7. 设置文件权限..."
chmod -R 755 public/
chown -R www:www public/

echo "=========================================="
echo "布局警告修复完成！"
echo "=========================================="
echo "已创建以下内容："
echo "- 关于页面 (about)"
echo "- 留言板页面 (guestbook)"
echo "- 吐槽页面 (shuoshuo)"
echo "- 分类页面 (categories)"
echo "- 标签页面 (tags)"
echo "- 示例文章 (5篇不同分类的文章)"
echo "- 布局模板文件"
echo ""
echo "请访问: https://www.stysky.cloud"
echo "现在应该不会再有布局警告了！" 