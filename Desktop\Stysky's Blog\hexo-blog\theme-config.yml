# Butterfly 主题配置文件
# 参考 blog.fzero.me 风格

# 主题颜色配置
theme_color:
  enable: true
  main: "#49b1f5"
  paginator: "#00c4b6"
  button_hover: "#FF7242"
  text_selection: "#00c4b6"
  link_color: "#99a9bf"
  meta_color: "#858585"
  hr_color: "#A4D8FA"
  code_color: "#F47466"
  code_bg_color: "#F8F8F8"
  toc_color: "#00c4b6"
  blockquote_padding_color: "#49b1f5"
  blockquote_background_color: "#49b1f5"

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/你的用户名 || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope
  twitter: https://twitter.com/你的用户名 || fab fa-twitter
  facebook: https://facebook.com/你的用户名 || fab fa-facebook
  youtube: https://youtube.com/你的频道 || fab fa-youtube
  instagram: https://instagram.com/你的用户名 || fab fa-instagram
  linkedin: https://linkedin.com/in/你的用户名 || fab fa-linkedin

# 侧边栏配置
aside:
  enable: true
  hide: false
  button: true
  mobile: true
  position: right
  display:
    archive: true
    tag: true
    category: true
  card_author:
    enable: true
    description: 允许一切自然发生
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me
      link: https://github.com/你的用户名
  card_announcement:
    enable: true
    content: 欢迎来到我的博客！
  card_recent_post:
    enable: true
    limit: 5
    sort: date
    sort_order: -1
  card_categories:
    enable: true
    limit: 8
    sort: name
    sort_order: 1
  card_tags:
    enable: true
    limit: 40
    sort: name
    sort_order: 1
    color: false
    initialize: false
  card_archives:
    enable: true
    type: monthly
    format: MMMM YYYY
    order: -1
    limit: 8
    sort: date
    sort_order: -1
  card_webinfo:
    enable: true
    post_count: true
    last_push_date: true

# 文章目录
toc:
  enable: true
  number: true
  expand: false
  style_simple: false

# 代码高亮
highlight_theme: mac
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 文章配置
post:
  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

  # 文章推荐
  recommend:
    enable: true
    limit: 6
    date: 90

  # 文章目录
  toc:
    enable: true
    number: true
    expand: false
    style_simple: false

  # 文章版权
  copyright:
    enable: true
    decode: false
    license: CC BY-NC-SA 4.0
    license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/
    link: https://creativecommons.org/licenses/by-nc-sa/4.0/
    info: 本博客所有文章除特别声明外，均采用 CC BY-NC-SA 4.0 许可协议。转载请保留原文链接！

# 搜索配置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 评论系统
comments:
  use: # 选择评论系统
  shortname: # 评论系统 shortname
  provider: # 评论系统提供商
  serverURLs: # 评论系统服务地址
  option: # 评论系统配置
  lazyload: false # 是否开启懒加载
  count: false # 是否显示评论数
  dns: false # 是否开启 DNS 预解析
  button: false # 是否显示评论按钮

# 页面配置
page:
  # 关于页面
  about:
    enable: true
    avatar: /images/avatar.jpg
    name: Stysky
    subtitle: 允许一切自然发生
    type: self
    comment: true
    social:
      github: https://github.com/你的用户名
      email: mailto:<EMAIL>
      twitter: https://twitter.com/你的用户名

  # 留言板页面
  guestbook:
    enable: true
    comment: true

  # 吐槽页面
  shuoshuo:
    enable: true
    comment: true

# 动画效果
animate:
  enable: true
  title: true
  sidebar: true
  post_list: true
  post_item: true
  post_header: true
  post_body: true
  post_footer: true
  comment: true

# 特效配置
pjax:
  enable: true
  exclude:
    - /music/
    - /js/
    - /css/
    - /img/

# 统计配置
statistics:
  enable: true
  busuanzi:
    enable: true
    total_visitors: true
    total_views: true
    post_views: true
  leancloud:
    enable: false
    app_id: # LeanCloud 应用 ID
    app_key: # LeanCloud 应用 Key
    server_urls: # LeanCloud 服务器地址

# 其他配置
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png

# 自定义 CSS
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>

# 自定义 JS
inject:
  head:
    - <script src="/js/custom-head.js"></script>
  bottom:
    - <script src="/js/custom-bottom.js"></script> 