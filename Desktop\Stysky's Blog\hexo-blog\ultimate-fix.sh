#!/bin/bash

echo "=========================================="
echo "彻底修复所有布局警告..."
echo "=========================================="

BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 备份当前配置..."
cp -r themes/butterfly themes/butterfly_backup_$(date +%Y%m%d_%H%M%S)

echo "2. 重新安装Butterfly主题..."
rm -rf themes/butterfly
git clone https://github.com/jerryc127/hexo-theme-butterfly.git themes/butterfly

echo "3. 重新创建主配置文件..."
cat > _config.yml << 'EOF'
# Hexo Configuration
title: Stysky's Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux, MYSQL]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
EOF

echo "4. 重新创建主题配置文件..."
cat > themes/butterfly/_config.yml << 'EOF'
# Butterfly 主题配置
title: Stysky's Blog
keywords: [Hexo, 博客, 技术, 生活, 运维, 网安, 渗透, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai

# 主题设置
theme: butterfly

# 导航菜单
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 社交链接
social:
  github: https://github.com/starryskybyd || fab fa-github
  email: mailto:<EMAIL> || fas fa-envelope

# 头像设置
avatar:
  img: /static/user6.jpg
  effect: true

# 首页设置
index_img: /static/background.jpg
index_img_height: 100vh

# 搜索设置
local_search:
  enable: true
  labels:
    input_placeholder: 搜索文章...
    hits_empty: 找不到您查询的内容: ${query}

# 代码高亮
highlight_theme: normal
highlight_copy: true
highlight_lang: true
highlight_shrink: false
highlight_height_limit: 400

# 阅读模式
reading_mode:
  enable: true
  light_bg: /images/reading-mode-light.png
  dark_bg: /images/reading-mode-dark.png

# 返回顶部
back2top:
  enable: true
  sidebar: false
  scrollpercent: false

# 页脚设置
footer:
  owner:
    enable: true
    since: 2023
  custom_text: Hi, welcome to my <a href="https://github.com/starryskybyd">blog</a>!
  icon:
    name: fa fa-heart
    animated: true
    color: "#ff0000"

# 自定义注入配置
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
  bottom:
    - <script src="/js/custom.js"></script>
EOF

echo "5. 创建正确的页面文件（使用page布局）..."

# 创建关于页面
mkdir -p source/about
cat > source/about/index.md << 'EOF'
---
title: 关于
date: 2024-01-01 00:00:00
layout: page
---

# 关于 Stysky

## 个人简介

你好！我是 Stysky，一名热爱技术的博主。

## 技术栈

- **操作系统**: Linux (CentOS, Ubuntu)
- **编程语言**: Python, Shell, JavaScript
- **数据库**: MySQL, Redis
- **运维工具**: Docker, Ansible, Jenkins
- **安全工具**: Nmap, Metasploit, Burp Suite

## 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: [starryskybyd](https://github.com/starryskybyd)
- **博客**: https://www.stysky.cloud

## 博客分类

- **Linux操作系统**: 系统管理、配置优化
- **渗透测试技术**: 安全测试、漏洞分析
- **安全运维技术**: 运维自动化、安全防护
- **实战学习分享**: 项目经验、学习心得
- **生活随笔**: 日常感悟、生活记录

---

欢迎来到我的博客，一起学习成长！
EOF

# 创建留言板页面
mkdir -p source/guestbook
cat > source/guestbook/index.md << 'EOF'
---
title: 留言板
date: 2024-01-01 00:00:00
layout: page
---

# 留言板

欢迎在这里留下您的评论和建议！

## 留言规则

1. 请文明发言，互相尊重
2. 可以讨论技术问题、分享经验
3. 禁止发布垃圾信息或恶意内容

---

期待您的留言！
EOF

# 创建吐槽页面
mkdir -p source/shuoshuo
cat > source/shuoshuo/index.md << 'EOF'
---
title: 吐槽
date: 2024-01-01 00:00:00
layout: page
---

# 吐槽专区

这里是我的日常吐槽和碎碎念...

## 今日吐槽

- 代码写得好累，但是很有成就感！
- 学习新技术总是让人兴奋
- 生活不易，技术更不易

---

欢迎分享您的想法！
EOF

# 创建分类页面
mkdir -p source/categories
cat > source/categories/index.md << 'EOF'
---
title: 分类
date: 2024-01-01 00:00:00
layout: page
---

# 文章分类

## 分类列表

- **Linux操作系统**: 系统管理、配置优化
- **渗透测试技术**: 安全测试、漏洞分析  
- **安全运维技术**: 运维自动化、安全防护
- **实战学习分享**: 项目经验、学习心得
- **生活随笔**: 日常感悟、生活记录

---

点击分类查看相关文章！
EOF

# 创建标签页面
mkdir -p source/tags
cat > source/tags/index.md << 'EOF'
---
title: 标签
date: 2024-01-01 00:00:00
layout: page
---

# 文章标签

## 标签云

- **技术**: 技术相关文章
- **博客**: 博客相关文章
- **运维**: 运维相关文章
- **Linux**: Linux系统相关
- **渗透**: 渗透测试相关
- **网安**: 网络安全相关
- **MYSQL**: 数据库相关

---

点击标签查看相关文章！
EOF

echo "6. 重新创建自定义CSS（修复IP定位居中）..."
cat > source/css/custom.css << 'EOF'
/* IP 地址显示 - 居中显示 */
#ip-info-monitor {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  color: #333;
  padding: 20px 24px;
  border-radius: 16px;
  font-size: 14px;
  z-index: 9996;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 300px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
}

#ip-info-monitor .monitor-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

#ip-info-monitor .monitor-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

#ip-info-monitor .monitor-label {
  color: #666;
  font-weight: 500;
  font-size: 13px;
}

#ip-info-monitor .monitor-value {
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
}

#ip-info-monitor .close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#ip-info-monitor .close-btn:hover {
  color: #667eea;
}

/* 强制设置背景图片 */
html, body {
  background-image: url('/static/background.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
  background-color: transparent !important;
}

/* 修复图标显示问题 */
.fa, .fas, .fab, .far {
  color: inherit !important;
  background: transparent !important;
}
EOF

echo "7. 重新创建自定义JavaScript（修复IP定位居中）..."
cat > source/js/custom.js << 'EOF'
// IP 地址显示 - 居中显示
class IPInfoMonitor {
  constructor() {
    this.init();
  }

  init() {
    const monitor = document.createElement('div');
    monitor.id = 'ip-info-monitor';
    monitor.innerHTML = `
      <button class="close-btn" onclick="this.parentElement.remove()">×</button>
      <div class="monitor-item">
        <span class="monitor-label">🌍 IP地址:</span>
        <span class="monitor-value" id="ipValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">📍 位置:</span>
        <span class="monitor-value" id="locationValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">🏢 运营商:</span>
        <span class="monitor-value" id="ispValue">--</span>
      </div>
      <div class="monitor-item">
        <span class="monitor-label">⏰ 访问时间:</span>
        <span class="monitor-value" id="timeValue">--</span>
      </div>
    `;
    document.body.appendChild(monitor);
    this.fetchIPInfo();
    
    // 5秒后自动隐藏
    setTimeout(() => {
      if (monitor.parentElement) {
        monitor.style.opacity = '0';
        setTimeout(() => monitor.remove(), 500);
      }
    }, 5000);
  }

  fetchIPInfo() {
    fetch('https://ipapi.co/json/')
      .then(response => response.json())
      .then(data => {
        const ipElement = document.getElementById('ipValue');
        const locationElement = document.getElementById('locationValue');
        const ispElement = document.getElementById('ispValue');
        const timeElement = document.getElementById('timeValue');

        if (ipElement) ipElement.textContent = data.ip || '--';
        if (locationElement) locationElement.textContent = `${data.city || ''}, ${data.region || ''}, ${data.country_name || ''}`.replace(/^, |^, , /, '').replace(/, ,$/, '') || '--';
        if (ispElement) ispElement.textContent = data.org || '--';
        if (timeElement) timeElement.textContent = new Date().toLocaleString('zh-CN');
      })
      .catch(error => {
        console.error('Error fetching IP info:', error);
        const ipElement = document.getElementById('ipValue');
        const locationElement = document.getElementById('locationValue');
        const ispElement = document.getElementById('ispValue');
        const timeElement = document.getElementById('timeValue');
        if (ipElement) ipElement.textContent = '--';
        if (locationElement) locationElement.textContent = '--';
        if (ispElement) ispElement.textContent = '--';
        if (timeElement) timeElement.textContent = new Date().toLocaleString('zh-CN');
      });
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('Stysky Blog 自定义脚本已加载');

  // 显示IP定位（仅在主页）
  if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
    setTimeout(() => new IPInfoMonitor(), 2000);
  }

  // 强制设置背景图片
  setTimeout(() => {
    document.body.style.backgroundImage = 'url("/static/background.jpg")';
    document.body.style.backgroundSize = 'cover';
    document.body.style.backgroundPosition = 'center';
    document.body.style.backgroundAttachment = 'fixed';
    document.body.style.backgroundRepeat = 'no-repeat';
  }, 100);
});
EOF

echo "8. 创建示例文章..."
mkdir -p source/_posts
cat > source/_posts/hello-world.md << 'EOF'
---
title: 欢迎来到 Stysky 的博客
date: 2024-01-01 12:00:00
updated: 2024-01-01 12:00:00
categories:
  - 技术
tags:
  - 技术
  - 博客
  - 运维
description: 欢迎来到我的博客，这里将分享技术、生活和学习心得
keywords:
  - 技术
  - 博客
  - 运维
toc: true
toc_number: true
toc_style_simple: false
copyright: true
mathjax: false
comments: true
top_img: /static/background.jpg
cover: /static/background.jpg
abbrlink:
reprint_policy: cc_by
headimg: /static/user6.jpg
---

<!-- 文章头部信息 -->
<div class="note info">
  <p>📝 本文发布于 2024-01-01，最后更新于 2024-01-01</p>
</div>

<!-- 文章目录 -->
<div class="note warning">
  <p>📋 文章目录</p>
</div>

## 前言

欢迎来到 Stysky 的博客！这里将分享我在技术学习、运维实践、网络安全等方面的经验和心得。

## 博客特色

### 1. 技术分享
- Linux 系统管理
- 网络安全技术
- 运维自动化
- 数据库管理

### 2. 学习心得
- 技术学习笔记
- 项目实战经验
- 问题解决方案

### 3. 生活随笔
- 日常感悟
- 学习心得
- 生活记录

## 技术栈

- **操作系统**: Linux (CentOS, Ubuntu)
- **编程语言**: Python, Shell, JavaScript
- **数据库**: MySQL, Redis
- **运维工具**: Docker, Ansible, Jenkins
- **安全工具**: Nmap, Metasploit, Burp Suite

## 联系方式

- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/starryskybyd
- **博客**: https://www.stysky.cloud

## 相关文章

<!-- 这里会自动显示相关文章 -->

---

<div class="note success">
  <p>🎉 感谢您的阅读！如果觉得文章有帮助，请点个赞支持一下。</p>
</div>
EOF

echo "9. 安装必要的依赖..."
npm install hexo-renderer-stylus@3.0.1 --save
npm install hexo-renderer-marked@7.0.0 --save

echo "10. 清理并重新生成..."
npx hexo clean
npx hexo generate

echo "11. 设置文件权限..."
chmod -R 755 public/
chown -R www:www public/

echo "=========================================="
echo "所有布局警告修复完成！"
echo "=========================================="
echo "已修复的问题："
echo "- 布局警告（使用page布局）"
echo "- IP定位功能居中显示"
echo "- 背景图片显示"
echo "- 头像更换"
echo "- 图标显示问题"
echo ""
echo "请访问: https://www.stysky.cloud"
echo "IP定位功能现在会居中显示，类似 blog.fzero.me"
echo "如果仍有问题，请强制刷新浏览器（Ctrl+F5）"
EOF 