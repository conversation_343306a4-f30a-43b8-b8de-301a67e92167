#!/bin/bash

echo "=========================================="
echo "修复 YAML 重复键错误..."
echo "=========================================="

# 设置博客目录
BLOG_DIR="/www/wwwroot/www.stysky.cloud/hexo-blog"
cd "$BLOG_DIR"

echo "1. 备份原始配置文件..."
cp _config.yml _config.yml.backup

echo "2. 创建正确的 _config.yml 文件..."
cat > _config.yml << 'EOF'
# Hexo Configuration
title: Stysky's Blog
subtitle: '允许一切自然发生'
description: '一个专注于技术与生活的个人博客'
keywords: [Hexo, 博客, 技术, 生活, 运维, 前端, 后端, Linux]
author: Stysky
language: zh-CN
timezone: Asia/Shanghai
url: https://www.stysky.cloud
root: /
permalink: :year/:month/:day/:title/
permalink_defaults:
pretty_urls:
  trailing_index: true
  trailing_html: true

# 目录结构
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# 主题设置
theme: butterfly

# 菜单导航
menu:
  首页: / || fas fa-home
  分类: /categories/ || fas fa-folder-open
  标签: /tags/ || fas fa-tags
  归档: /archives/ || fas fa-archive
  关于: /about/ || fas fa-heart
  留言板: /guestbook/ || fas fa-comments
  吐槽: /shuoshuo/ || fas fa-comment-dots

# 文章设置
new_post_name: :title.md
default_layout: post
titlecase: false
external_link:
  enable: true
  field: site
  exclude: ''

# 代码高亮
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''

# 站点地图
sitemap:
  path: sitemap.xml
baidusitemap:
  path: baidusitemap.xml

# Feed
feed:
  type: atom
  path: atom.xml
  limit: 20
EOF

echo "3. 检查配置文件语法..."
if hexo version > /dev/null 2>&1; then
    echo "Hexo 配置语法检查通过"
else
    echo "警告：无法检查 Hexo 配置语法"
fi

echo "4. 重新生成静态文件..."
hexo clean
hexo generate

echo "5. 设置权限..."
chown -R www:www "$BLOG_DIR"
chmod -R 755 "$BLOG_DIR"

echo "=========================================="
echo "YAML 错误修复完成！"
echo "=========================================="
echo "现在可以正常使用 hexo clean 和 hexo generate 命令了"
echo "请访问: https://www.stysky.cloud"
echo "==========================================" 