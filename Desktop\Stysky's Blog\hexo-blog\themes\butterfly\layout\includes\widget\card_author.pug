if theme.aside.card_author.enable
  .card-widget.card-info.text-center
    .avatar-img
      img(src=url_for(theme.avatar.img) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt="avatar")
    .author-info-name= config.author
    .author-info-description!= theme.aside.card_author.description || config.description

    .site-data
      a(href=url_for(config.archive_dir) + '/')
        .headline= _p('aside.articles')
        .length-num= site.posts.length
      a(href=url_for(config.tag_dir) + '/')
        .headline= _p('aside.tags')
        .length-num= site.tags.length
      a(href=url_for(config.category_dir) + '/')
        .headline= _p('aside.categories')
        .length-num= site.categories.length

    if theme.aside.card_author.button.enable
      a#card-info-btn(href=theme.aside.card_author.button.link)
        i(class=theme.aside.card_author.button.icon)
        span=theme.aside.card_author.button.text

    if(theme.social)
      .card-info-social-icons
        !=partial('includes/header/social', {}, {cache: true})
