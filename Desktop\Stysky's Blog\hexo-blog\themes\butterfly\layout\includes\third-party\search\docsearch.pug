- const { placeholder, docsearch: { appId, apiKey, indexName, option } } = theme.search

.docsearch-wrap
  #docsearch(style="display:none")
  link(rel="stylesheet" href=url_for(theme.asset.docsearch_css))
  script(src=url_for(theme.asset.docsearch_js))
  script.
    (() => {
      docsearch(Object.assign({
        appId: '!{appId}',
        apiKey: '!{apiKey}',
        indexName: '!{indexName}',
        container: '#docsearch',
        placeholder: '!{ placeholder || _p("search.input_placeholder")}',
      }, !{JSON.stringify(option)}))

      const handleClick = () => {
        document.querySelector('.DocSearch-Button').click()
      }

      const searchClickFn = () => {
        btf.addEventListenerPjax(document.querySelector('#search-button > .search'), 'click', handleClick)
      }

      searchClickFn()
      window.addEventListener('pjax:complete', searchClickFn)
    })()


